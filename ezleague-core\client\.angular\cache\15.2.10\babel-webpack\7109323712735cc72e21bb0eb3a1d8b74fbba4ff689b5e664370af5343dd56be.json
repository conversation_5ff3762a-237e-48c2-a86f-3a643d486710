{"ast": null, "code": "import baseSetToString from './_baseSetToString.js';\nimport shortOut from './_shortOut.js';\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\nexport default setToString;", "map": {"version": 3, "names": ["baseSetToString", "shortOut", "setToString"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_setToString.js"], "sourcesContent": ["import baseSetToString from './_baseSetToString.js';\nimport shortOut from './_shortOut.js';\n\n/**\n * Sets the `toString` method of `func` to return `string`.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar setToString = shortOut(baseSetToString);\n\nexport default setToString;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAGD,QAAQ,CAACD,eAAe,CAAC;AAE3C,eAAeE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}