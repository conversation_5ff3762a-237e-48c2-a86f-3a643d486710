{"ast": null, "code": "/**\n * The base implementation of `_.sum` and `_.sumBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the sum.\n */\nfunction baseSum(array, iteratee) {\n  var result,\n    index = -1,\n    length = array.length;\n  while (++index < length) {\n    var current = iteratee(array[index]);\n    if (current !== undefined) {\n      result = result === undefined ? current : result + current;\n    }\n  }\n  return result;\n}\nexport default baseSum;", "map": {"version": 3, "names": ["baseSum", "array", "iteratee", "result", "index", "length", "current", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_baseSum.js"], "sourcesContent": ["/**\n * The base implementation of `_.sum` and `_.sumBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the sum.\n */\nfunction baseSum(array, iteratee) {\n  var result,\n      index = -1,\n      length = array.length;\n\n  while (++index < length) {\n    var current = iteratee(array[index]);\n    if (current !== undefined) {\n      result = result === undefined ? current : (result + current);\n    }\n  }\n  return result;\n}\n\nexport default baseSum;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAChC,IAAIC,MAAM;IACNC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAEzB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIC,OAAO,GAAGJ,QAAQ,CAACD,KAAK,CAACG,KAAK,CAAC,CAAC;IACpC,IAAIE,OAAO,KAAKC,SAAS,EAAE;MACzBJ,MAAM,GAAGA,MAAM,KAAKI,SAAS,GAAGD,OAAO,GAAIH,MAAM,GAAGG,OAAQ;IAC9D;EACF;EACA,OAAOH,MAAM;AACf;AAEA,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}