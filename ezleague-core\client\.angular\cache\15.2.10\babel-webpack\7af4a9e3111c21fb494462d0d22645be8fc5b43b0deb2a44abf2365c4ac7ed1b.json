{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseXor from './_baseXor.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.xor` except that it accepts `iteratee` which is\n * invoked for each element of each `arrays` to generate the criterion by\n * which by which they're compared. The order of result values is determined\n * by the order they occur in the arrays. The iteratee is invoked with one\n * argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.xorBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [1.2, 3.4]\n *\n * // The `_.property` iteratee shorthand.\n * _.xorBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 2 }]\n */\nvar xorBy = baseRest(function (arrays) {\n  var iteratee = last(arrays);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return baseXor(arrayFilter(arrays, isArrayLikeObject), baseIteratee(iteratee, 2));\n});\nexport default xorBy;", "map": {"version": 3, "names": ["arrayFilter", "baseIteratee", "baseRest", "baseXor", "isArrayLikeObject", "last", "xorBy", "arrays", "iteratee", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/xorBy.js"], "sourcesContent": ["import arrayFilter from './_arrayFilter.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseXor from './_baseXor.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.xor` except that it accepts `iteratee` which is\n * invoked for each element of each `arrays` to generate the criterion by\n * which by which they're compared. The order of result values is determined\n * by the order they occur in the arrays. The iteratee is invoked with one\n * argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.xorBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [1.2, 3.4]\n *\n * // The `_.property` iteratee shorthand.\n * _.xorBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 2 }]\n */\nvar xorBy = baseRest(function(arrays) {\n  var iteratee = last(arrays);\n  if (isArrayLikeObject(iteratee)) {\n    iteratee = undefined;\n  }\n  return baseXor(arrayFilter(arrays, isArrayLikeObject), baseIteratee(iteratee, 2));\n});\n\nexport default xorBy;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGJ,QAAQ,CAAC,UAASK,MAAM,EAAE;EACpC,IAAIC,QAAQ,GAAGH,IAAI,CAACE,MAAM,CAAC;EAC3B,IAAIH,iBAAiB,CAACI,QAAQ,CAAC,EAAE;IAC/BA,QAAQ,GAAGC,SAAS;EACtB;EACA,OAAON,OAAO,CAACH,WAAW,CAACO,MAAM,EAAEH,iBAAiB,CAAC,EAAEH,YAAY,CAACO,QAAQ,EAAE,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC;AAEF,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}