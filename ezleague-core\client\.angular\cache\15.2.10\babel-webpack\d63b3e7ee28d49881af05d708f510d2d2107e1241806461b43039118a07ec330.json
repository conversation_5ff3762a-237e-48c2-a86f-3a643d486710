{"ast": null, "code": "import baseClone from './_baseClone.js';\nimport baseMatchesProperty from './_baseMatchesProperty.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1;\n\n/**\n * Creates a function that performs a partial deep comparison between the\n * value at `path` of a given object to `srcValue`, returning `true` if the\n * object value is equivalent, else `false`.\n *\n * **Note:** Partial comparisons will match empty array and empty object\n * `srcValue` values against any array or object value, respectively. See\n * `_.isEqual` for a list of supported value comparisons.\n *\n * **Note:** Multiple values can be checked by combining several matchers\n * using `_.overSome`\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Util\n * @param {Array|string} path The path of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n * @example\n *\n * var objects = [\n *   { 'a': 1, 'b': 2, 'c': 3 },\n *   { 'a': 4, 'b': 5, 'c': 6 }\n * ];\n *\n * _.find(objects, _.matchesProperty('a', 4));\n * // => { 'a': 4, 'b': 5, 'c': 6 }\n *\n * // Checking for several possible values\n * _.filter(objects, _.overSome([_.matchesProperty('a', 1), _.matchesProperty('a', 4)]));\n * // => [{ 'a': 1, 'b': 2, 'c': 3 }, { 'a': 4, 'b': 5, 'c': 6 }]\n */\nfunction matchesProperty(path, srcValue) {\n  return baseMatchesProperty(path, baseClone(srcValue, CLONE_DEEP_FLAG));\n}\nexport default matchesProperty;", "map": {"version": 3, "names": ["baseClone", "baseMatchesProperty", "CLONE_DEEP_FLAG", "matchesProperty", "path", "srcValue"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/matchesProperty.js"], "sourcesContent": ["import baseClone from './_baseClone.js';\nimport baseMatchesProperty from './_baseMatchesProperty.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1;\n\n/**\n * Creates a function that performs a partial deep comparison between the\n * value at `path` of a given object to `srcValue`, returning `true` if the\n * object value is equivalent, else `false`.\n *\n * **Note:** Partial comparisons will match empty array and empty object\n * `srcValue` values against any array or object value, respectively. See\n * `_.isEqual` for a list of supported value comparisons.\n *\n * **Note:** Multiple values can be checked by combining several matchers\n * using `_.overSome`\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Util\n * @param {Array|string} path The path of the property to get.\n * @param {*} srcValue The value to match.\n * @returns {Function} Returns the new spec function.\n * @example\n *\n * var objects = [\n *   { 'a': 1, 'b': 2, 'c': 3 },\n *   { 'a': 4, 'b': 5, 'c': 6 }\n * ];\n *\n * _.find(objects, _.matchesProperty('a', 4));\n * // => { 'a': 4, 'b': 5, 'c': 6 }\n *\n * // Checking for several possible values\n * _.filter(objects, _.overSome([_.matchesProperty('a', 1), _.matchesProperty('a', 4)]));\n * // => [{ 'a': 1, 'b': 2, 'c': 3 }, { 'a': 4, 'b': 5, 'c': 6 }]\n */\nfunction matchesProperty(path, srcValue) {\n  return baseMatchesProperty(path, baseClone(srcValue, CLONE_DEEP_FLAG));\n}\n\nexport default matchesProperty;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,mBAAmB,MAAM,2BAA2B;;AAE3D;AACA,IAAIC,eAAe,GAAG,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAe,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACvC,OAAOJ,mBAAmB,CAACG,IAAI,EAAEJ,SAAS,CAACK,QAAQ,EAAEH,eAAe,CAAC,CAAC;AACxE;AAEA,eAAeC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}