{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport copyArray from './_copyArray.js';\nimport isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\nimport stringToPath from './_stringToPath.js';\nimport toKey from './_toKey.js';\nimport toString from './toString.js';\n\n/**\n * Converts `value` to a property path array.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {*} value The value to convert.\n * @returns {Array} Returns the new property path array.\n * @example\n *\n * _.toPath('a.b.c');\n * // => ['a', 'b', 'c']\n *\n * _.toPath('a[0].b.c');\n * // => ['a', '0', 'b', 'c']\n */\nfunction toPath(value) {\n  if (isArray(value)) {\n    return arrayMap(value, toKey);\n  }\n  return isSymbol(value) ? [value] : copyArray(stringToPath(toString(value)));\n}\nexport default toPath;", "map": {"version": 3, "names": ["arrayMap", "copyArray", "isArray", "isSymbol", "stringToPath", "to<PERSON><PERSON>", "toString", "to<PERSON><PERSON>", "value"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/toPath.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport copyArray from './_copyArray.js';\nimport isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\nimport stringToPath from './_stringToPath.js';\nimport toKey from './_toKey.js';\nimport toString from './toString.js';\n\n/**\n * Converts `value` to a property path array.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {*} value The value to convert.\n * @returns {Array} Returns the new property path array.\n * @example\n *\n * _.toPath('a.b.c');\n * // => ['a', 'b', 'c']\n *\n * _.toPath('a[0].b.c');\n * // => ['a', '0', 'b', 'c']\n */\nfunction toPath(value) {\n  if (isArray(value)) {\n    return arrayMap(value, toKey);\n  }\n  return isSymbol(value) ? [value] : copyArray(stringToPath(toString(value)));\n}\n\nexport default toPath;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAM,CAACC,KAAK,EAAE;EACrB,IAAIN,OAAO,CAACM,KAAK,CAAC,EAAE;IAClB,OAAOR,QAAQ,CAACQ,KAAK,EAAEH,KAAK,CAAC;EAC/B;EACA,OAAOF,QAAQ,CAACK,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,GAAGP,SAAS,CAACG,YAAY,CAACE,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAC;AAC7E;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}