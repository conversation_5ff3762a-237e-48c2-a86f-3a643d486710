<?php

namespace App\Utils;

use Carbon\Carbon;
use DateTimeZone;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class TimezoneHelper
{
    /**
     * Cache timezone objects to avoid repeated instantiation
     */
    private static array $timezoneCache = [];

    /**
     * Get a cached timezone object
     */
    public static function getTimezone(string $timezone): DateTimeZone
    {
        if (!isset(self::$timezoneCache[$timezone])) {
            try {
                self::$timezoneCache[$timezone] = new DateTimeZone($timezone);
            } catch (Exception $e) {
                Log::warning("Invalid timezone '{$timezone}', falling back to UTC", [
                    'error' => $e->getMessage()
                ]);
                self::$timezoneCache[$timezone] = new DateTimeZone('UTC');
            }
        }

        return self::$timezoneCache[$timezone];
    }

    /**
     * Convert datetime to UTC for database storage
     * Optimized for batch operations
     */
    public static function toUtc(string $datetime, string $fromTimezone = 'UTC'): string
    {
        if ($fromTimezone === 'UTC') {
            return $datetime; // Already UTC, no conversion needed
        }

        try {
            $carbon = Carbon::parse($datetime, self::getTimezone($fromTimezone));
            return $carbon->utc()->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            Log::error("Failed to convert datetime to UTC", [
                'datetime' => $datetime,
                'timezone' => $fromTimezone,
                'error' => $e->getMessage()
            ]);
            return $datetime; // Return original on error
        }
    }

    /**
     * Convert UTC datetime to specified timezone
     * Optimized for display purposes
     */
    public static function fromUtc(string $utcDatetime, string $toTimezone = 'UTC'): Carbon
    {
        if ($toTimezone === 'UTC') {
            return Carbon::parse($utcDatetime, 'UTC');
        }

        try {
            return Carbon::parse($utcDatetime, 'UTC')->setTimezone(self::getTimezone($toTimezone));
        } catch (Exception $e) {
            Log::error("Failed to convert UTC datetime to timezone", [
                'datetime' => $utcDatetime,
                'timezone' => $toTimezone,
                'error' => $e->getMessage()
            ]);
            return Carbon::parse($utcDatetime, 'UTC'); // Return UTC on error
        }
    }

    /**
     * Batch convert multiple datetimes to UTC
     * More efficient than individual conversions
     */
    public static function batchToUtc(array $datetimes, string $fromTimezone = 'UTC'): array
    {
        if ($fromTimezone === 'UTC') {
            return $datetimes; // Already UTC
        }

        $timezone = self::getTimezone($fromTimezone);
        $results = [];

        foreach ($datetimes as $key => $datetime) {
            try {
                $carbon = Carbon::parse($datetime, $timezone);
                $results[$key] = $carbon->utc()->format('Y-m-d H:i:s');
            } catch (Exception $e) {
                Log::error("Failed to batch convert datetime to UTC", [
                    'key' => $key,
                    'datetime' => $datetime,
                    'timezone' => $fromTimezone,
                    'error' => $e->getMessage()
                ]);
                $results[$key] = $datetime; // Keep original on error
            }
        }

        return $results;
    }

    /**
     * Get timezone offset in minutes for efficient date filtering
     * Cached for performance
     */
    public static function getTimezoneOffset(string $timezone, string $date = null): int
    {
        $cacheKey = "timezone_offset_{$timezone}_" . ($date ?? date('Y-m-d'));
        
        return Cache::remember($cacheKey, 3600, function () use ($timezone, $date) {
            try {
                $tz = self::getTimezone($timezone);
                $dateTime = Carbon::parse($date ?? 'now', $tz);
                return $dateTime->getOffset() / 60; // Convert seconds to minutes
            } catch (Exception $e) {
                Log::error("Failed to get timezone offset", [
                    'timezone' => $timezone,
                    'date' => $date,
                    'error' => $e->getMessage()
                ]);
                return 0; // UTC offset
            }
        });
    }

    /**
     * Create efficient date range filters for database queries
     * Accounts for timezone boundaries
     */
    public static function createDateRangeFilter(string $date, string $timezone = 'UTC'): array
    {
        if ($timezone === 'UTC') {
            return [
                'start' => $date . ' 00:00:00',
                'end' => $date . ' 23:59:59'
            ];
        }

        try {
            $tz = self::getTimezone($timezone);
            
            // Start of day in the specified timezone, converted to UTC
            $startOfDay = Carbon::parse($date . ' 00:00:00', $tz)->utc();
            
            // End of day in the specified timezone, converted to UTC
            $endOfDay = Carbon::parse($date . ' 23:59:59', $tz)->utc();

            return [
                'start' => $startOfDay->format('Y-m-d H:i:s'),
                'end' => $endOfDay->format('Y-m-d H:i:s')
            ];
        } catch (Exception $e) {
            Log::error("Failed to create date range filter", [
                'date' => $date,
                'timezone' => $timezone,
                'error' => $e->getMessage()
            ]);
            
            // Fallback to UTC
            return [
                'start' => $date . ' 00:00:00',
                'end' => $date . ' 23:59:59'
            ];
        }
    }

    /**
     * Validate timezone string
     */
    public static function isValidTimezone(string $timezone): bool
    {
        try {
            new DateTimeZone($timezone);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get request timezone with fallback
     */
    public static function getRequestTimezone(): string
    {
        $timezone = request()->header('X-Time-Zone') ?? config('app.timezone', 'UTC');
        
        return self::isValidTimezone($timezone) ? $timezone : 'UTC';
    }

    /**
     * Clear timezone cache (useful for testing)
     */
    public static function clearCache(): void
    {
        self::$timezoneCache = [];
        Cache::forget('timezone_offset_*');
    }

    /**
     * Format duration in a human-readable way
     */
    public static function formatDuration(int $minutes): string
    {
        if ($minutes < 60) {
            return $minutes . ' minutes';
        }

        $hours = intval($minutes / 60);
        $remainingMinutes = $minutes % 60;

        if ($remainingMinutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }

        return $hours . 'h ' . $remainingMinutes . 'm';
    }

    /**
     * Calculate business hours overlap for scheduling optimization
     */
    public static function calculateBusinessHoursOverlap(
        string $timezone1, 
        string $timezone2, 
        array $businessHours = ['09:00', '17:00']
    ): array {
        try {
            $tz1 = self::getTimezone($timezone1);
            $tz2 = self::getTimezone($timezone2);
            
            // Convert business hours to UTC for both timezones
            $start1 = Carbon::parse('today ' . $businessHours[0], $tz1)->utc();
            $end1 = Carbon::parse('today ' . $businessHours[1], $tz1)->utc();
            
            $start2 = Carbon::parse('today ' . $businessHours[0], $tz2)->utc();
            $end2 = Carbon::parse('today ' . $businessHours[1], $tz2)->utc();
            
            // Find overlap
            $overlapStart = $start1->max($start2);
            $overlapEnd = $end1->min($end2);
            
            if ($overlapStart >= $overlapEnd) {
                return []; // No overlap
            }
            
            return [
                'start' => $overlapStart->format('H:i'),
                'end' => $overlapEnd->format('H:i'),
                'duration_minutes' => $overlapStart->diffInMinutes($overlapEnd)
            ];
        } catch (Exception $e) {
            Log::error("Failed to calculate business hours overlap", [
                'timezone1' => $timezone1,
                'timezone2' => $timezone2,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
