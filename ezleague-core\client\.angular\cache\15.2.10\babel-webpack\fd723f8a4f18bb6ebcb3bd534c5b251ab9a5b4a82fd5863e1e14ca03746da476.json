{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` from `start` up to, but not including, `end`.\n *\n * **Note:** This method is used instead of\n * [`Array#slice`](https://mdn.io/Array/slice) to ensure dense arrays are\n * returned.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction slice(array, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (end && typeof end != 'number' && isIterateeCall(array, start, end)) {\n    start = 0;\n    end = length;\n  } else {\n    start = start == null ? 0 : toInteger(start);\n    end = end === undefined ? length : toInteger(end);\n  }\n  return baseSlice(array, start, end);\n}\nexport default slice;", "map": {"version": 3, "names": ["baseSlice", "isIterateeCall", "toInteger", "slice", "array", "start", "end", "length", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/slice.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` from `start` up to, but not including, `end`.\n *\n * **Note:** This method is used instead of\n * [`Array#slice`](https://mdn.io/Array/slice) to ensure dense arrays are\n * returned.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction slice(array, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (end && typeof end != 'number' && isIterateeCall(array, start, end)) {\n    start = 0;\n    end = length;\n  }\n  else {\n    start = start == null ? 0 : toInteger(start);\n    end = end === undefined ? length : toInteger(end);\n  }\n  return baseSlice(array, start, end);\n}\n\nexport default slice;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAK,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAChC,IAAIC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EACA,IAAID,GAAG,IAAI,OAAOA,GAAG,IAAI,QAAQ,IAAIL,cAAc,CAACG,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC,EAAE;IACtED,KAAK,GAAG,CAAC;IACTC,GAAG,GAAGC,MAAM;EACd,CAAC,MACI;IACHF,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGH,SAAS,CAACG,KAAK,CAAC;IAC5CC,GAAG,GAAGA,GAAG,KAAKE,SAAS,GAAGD,MAAM,GAAGL,SAAS,CAACI,GAAG,CAAC;EACnD;EACA,OAAON,SAAS,CAACI,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;AACrC;AAEA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}