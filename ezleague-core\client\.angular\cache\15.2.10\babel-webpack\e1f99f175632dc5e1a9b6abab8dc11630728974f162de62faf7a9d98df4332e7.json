{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements taken from the beginning.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to take.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.take([1, 2, 3]);\n * // => [1]\n *\n * _.take([1, 2, 3], 2);\n * // => [1, 2]\n *\n * _.take([1, 2, 3], 5);\n * // => [1, 2, 3]\n *\n * _.take([1, 2, 3], 0);\n * // => []\n */\nfunction take(array, n, guard) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  n = guard || n === undefined ? 1 : toInteger(n);\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\nexport default take;", "map": {"version": 3, "names": ["baseSlice", "toInteger", "take", "array", "n", "guard", "length", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/take.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a slice of `array` with `n` elements taken from the beginning.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to take.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.take([1, 2, 3]);\n * // => [1]\n *\n * _.take([1, 2, 3], 2);\n * // => [1, 2]\n *\n * _.take([1, 2, 3], 5);\n * // => [1, 2, 3]\n *\n * _.take([1, 2, 3], 0);\n * // => []\n */\nfunction take(array, n, guard) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  n = (guard || n === undefined) ? 1 : toInteger(n);\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\n\nexport default take;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAI,CAACC,KAAK,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAC7B,IAAI,EAAEF,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC,EAAE;IAC5B,OAAO,EAAE;EACX;EACAF,CAAC,GAAIC,KAAK,IAAID,CAAC,KAAKG,SAAS,GAAI,CAAC,GAAGN,SAAS,CAACG,CAAC,CAAC;EACjD,OAAOJ,SAAS,CAACG,KAAK,EAAE,CAAC,EAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;AAC3C;AAEA,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}