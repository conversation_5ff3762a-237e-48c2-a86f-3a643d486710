{"ast": null, "code": "import castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/**\n * Creates a function like `_.lowerFirst`.\n *\n * @private\n * @param {string} methodName The name of the `String` case method to use.\n * @returns {Function} Returns the new case function.\n */\nfunction createCaseFirst(methodName) {\n  return function (string) {\n    string = toString(string);\n    var strSymbols = hasUnicode(string) ? stringToArray(string) : undefined;\n    var chr = strSymbols ? strSymbols[0] : string.charAt(0);\n    var trailing = strSymbols ? castSlice(strSymbols, 1).join('') : string.slice(1);\n    return chr[methodName]() + trailing;\n  };\n}\nexport default createCaseFirst;", "map": {"version": 3, "names": ["castSlice", "hasUnicode", "stringToArray", "toString", "createCaseFirst", "methodName", "string", "strSymbols", "undefined", "chr", "char<PERSON>t", "trailing", "join", "slice"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_createCaseFirst.js"], "sourcesContent": ["import castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/**\n * Creates a function like `_.lowerFirst`.\n *\n * @private\n * @param {string} methodName The name of the `String` case method to use.\n * @returns {Function} Returns the new case function.\n */\nfunction createCaseFirst(methodName) {\n  return function(string) {\n    string = toString(string);\n\n    var strSymbols = hasUnicode(string)\n      ? stringToArray(string)\n      : undefined;\n\n    var chr = strSymbols\n      ? strSymbols[0]\n      : string.charAt(0);\n\n    var trailing = strSymbols\n      ? castSlice(strSymbols, 1).join('')\n      : string.slice(1);\n\n    return chr[methodName]() + trailing;\n  };\n}\n\nexport default createCaseFirst;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAe,CAACC,UAAU,EAAE;EACnC,OAAO,UAASC,MAAM,EAAE;IACtBA,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;IAEzB,IAAIC,UAAU,GAAGN,UAAU,CAACK,MAAM,CAAC,GAC/BJ,aAAa,CAACI,MAAM,CAAC,GACrBE,SAAS;IAEb,IAAIC,GAAG,GAAGF,UAAU,GAChBA,UAAU,CAAC,CAAC,CAAC,GACbD,MAAM,CAACI,MAAM,CAAC,CAAC,CAAC;IAEpB,IAAIC,QAAQ,GAAGJ,UAAU,GACrBP,SAAS,CAACO,UAAU,EAAE,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,GACjCN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC;IAEnB,OAAOJ,GAAG,CAACJ,UAAU,CAAC,EAAE,GAAGM,QAAQ;EACrC,CAAC;AACH;AAEA,eAAeP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}