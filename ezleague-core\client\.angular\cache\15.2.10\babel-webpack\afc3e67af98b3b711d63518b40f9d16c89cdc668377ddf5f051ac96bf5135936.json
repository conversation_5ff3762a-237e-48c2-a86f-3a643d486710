{"ast": null, "code": "/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\nexport default stackGet;", "map": {"version": 3, "names": ["stackGet", "key", "__data__", "get"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_stackGet.js"], "sourcesContent": ["/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\nexport default stackGet;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,CAACC,GAAG,EAAE;EACrB,OAAO,IAAI,CAACC,QAAQ,CAACC,GAAG,CAACF,GAAG,CAAC;AAC/B;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}