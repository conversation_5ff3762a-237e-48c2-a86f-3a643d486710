{"ast": null, "code": "const EXP_TABLE = new Uint8Array(512);\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */;\n(function initTables() {\n  let x = 1;\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x;\n    LOG_TABLE[x] = i;\n    x <<= 1; // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) {\n      // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D;\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255];\n  }\n})();\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log(n) {\n  if (n < 1) throw new Error('log(' + n + ')');\n  return LOG_TABLE[n];\n};\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp(n) {\n  return EXP_TABLE[n];\n};\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul(x, y) {\n  if (x === 0 || y === 0) return 0;\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]];\n};", "map": {"version": 3, "names": ["EXP_TABLE", "Uint8Array", "LOG_TABLE", "initTables", "x", "i", "exports", "log", "n", "Error", "exp", "mul", "y"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/@cordobo/qrcode/lib/core/galois-field.js"], "sourcesContent": ["const EXP_TABLE = new Uint8Array(512)\nconst LOG_TABLE = new Uint8Array(256)\n/**\n * Precompute the log and anti-log tables for faster computation later\n *\n * For each possible value in the galois field 2^8, we will pre-compute\n * the logarithm and anti-logarithm (exponential) of this value\n *\n * ref {@link https://en.wikiversity.org/wiki/Reed%E2%80%93Solomon_codes_for_coders#Introduction_to_mathematical_fields}\n */\n;(function initTables () {\n  let x = 1\n  for (let i = 0; i < 255; i++) {\n    EXP_TABLE[i] = x\n    LOG_TABLE[x] = i\n\n    x <<= 1 // multiply by 2\n\n    // The QR code specification says to use byte-wise modulo 100011101 arithmetic.\n    // This means that when a number is 256 or larger, it should be XORed with 0x11D.\n    if (x & 0x100) { // similar to x >= 256, but a lot faster (because 0x100 == 256)\n      x ^= 0x11D\n    }\n  }\n\n  // Optimization: double the size of the anti-log table so that we don't need to mod 255 to\n  // stay inside the bounds (because we will mainly use this table for the multiplication of\n  // two GF numbers, no more).\n  // @see {@link mul}\n  for (let i = 255; i < 512; i++) {\n    EXP_TABLE[i] = EXP_TABLE[i - 255]\n  }\n}())\n\n/**\n * Returns log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.log = function log (n) {\n  if (n < 1) throw new Error('log(' + n + ')')\n  return LOG_TABLE[n]\n}\n\n/**\n * Returns anti-log value of n inside Galois Field\n *\n * @param  {Number} n\n * @return {Number}\n */\nexports.exp = function exp (n) {\n  return EXP_TABLE[n]\n}\n\n/**\n * Multiplies two number inside Galois Field\n *\n * @param  {Number} x\n * @param  {Number} y\n * @return {Number}\n */\nexports.mul = function mul (x, y) {\n  if (x === 0 || y === 0) return 0\n\n  // should be EXP_TABLE[(LOG_TABLE[x] + LOG_TABLE[y]) % 255] if EXP_TABLE wasn't oversized\n  // @see {@link initTables}\n  return EXP_TABLE[LOG_TABLE[x] + LOG_TABLE[y]]\n}\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG,IAAIC,UAAU,CAAC,GAAG,CAAC;AACrC,MAAMC,SAAS,GAAG,IAAID,UAAU,CAAC,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAPA;AAQE,UAASE,UAAU,GAAI;EACvB,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5BL,SAAS,CAACK,CAAC,CAAC,GAAGD,CAAC;IAChBF,SAAS,CAACE,CAAC,CAAC,GAAGC,CAAC;IAEhBD,CAAC,KAAK,CAAC,EAAC;;IAER;IACA;IACA,IAAIA,CAAC,GAAG,KAAK,EAAE;MAAE;MACfA,CAAC,IAAI,KAAK;IACZ;EACF;;EAEA;EACA;EACA;EACA;EACA,KAAK,IAAIC,CAAC,GAAG,GAAG,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC9BL,SAAS,CAACK,CAAC,CAAC,GAAGL,SAAS,CAACK,CAAC,GAAG,GAAG,CAAC;EACnC;AACF,CAAC,GAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACAC,OAAO,CAACC,GAAG,GAAG,SAASA,GAAG,CAAEC,CAAC,EAAE;EAC7B,IAAIA,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,MAAM,GAAGD,CAAC,GAAG,GAAG,CAAC;EAC5C,OAAON,SAAS,CAACM,CAAC,CAAC;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACI,GAAG,GAAG,SAASA,GAAG,CAAEF,CAAC,EAAE;EAC7B,OAAOR,SAAS,CAACQ,CAAC,CAAC;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,OAAO,CAACK,GAAG,GAAG,SAASA,GAAG,CAAEP,CAAC,EAAEQ,CAAC,EAAE;EAChC,IAAIR,CAAC,KAAK,CAAC,IAAIQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;;EAEhC;EACA;EACA,OAAOZ,SAAS,CAACE,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACU,CAAC,CAAC,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}