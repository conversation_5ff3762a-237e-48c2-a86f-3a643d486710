{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n  nativeMax = Math.max;\n\n/**\n * The base implementation of `_.range` and `_.rangeRight` which doesn't\n * coerce arguments.\n *\n * @private\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @param {number} step The value to increment or decrement by.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the range of numbers.\n */\nfunction baseRange(start, end, step, fromRight) {\n  var index = -1,\n    length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),\n    result = Array(length);\n  while (length--) {\n    result[fromRight ? length : ++index] = start;\n    start += step;\n  }\n  return result;\n}\nexport default baseRange;", "map": {"version": 3, "names": ["nativeCeil", "Math", "ceil", "nativeMax", "max", "baseRange", "start", "end", "step", "fromRight", "index", "length", "result", "Array"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_baseRange.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * The base implementation of `_.range` and `_.rangeRight` which doesn't\n * coerce arguments.\n *\n * @private\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @param {number} step The value to increment or decrement by.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Array} Returns the range of numbers.\n */\nfunction baseRange(start, end, step, fromRight) {\n  var index = -1,\n      length = nativeMax(nativeCeil((end - start) / (step || 1)), 0),\n      result = Array(length);\n\n  while (length--) {\n    result[fromRight ? length : ++index] = start;\n    start += step;\n  }\n  return result;\n}\n\nexport default baseRange;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAGC,IAAI,CAACC,IAAI;EACtBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAAS,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC9C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGR,SAAS,CAACH,UAAU,CAAC,CAACO,GAAG,GAAGD,KAAK,KAAKE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9DI,MAAM,GAAGC,KAAK,CAACF,MAAM,CAAC;EAE1B,OAAOA,MAAM,EAAE,EAAE;IACfC,MAAM,CAACH,SAAS,GAAGE,MAAM,GAAG,EAAED,KAAK,CAAC,GAAGJ,KAAK;IAC5CA,KAAK,IAAIE,IAAI;EACf;EACA,OAAOI,MAAM;AACf;AAEA,eAAeP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}