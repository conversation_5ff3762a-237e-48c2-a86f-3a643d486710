{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * Removes leading whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimStart('  abc  ');\n * // => 'abc  '\n *\n * _.trimStart('-_-abc-_-', '_-');\n * // => 'abc-_-'\n */\nfunction trimStart(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.replace(reTrimStart, '');\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n    start = charsStartIndex(strSymbols, stringToArray(chars));\n  return castSlice(strSymbols, start).join('');\n}\nexport default trimStart;", "map": {"version": 3, "names": ["baseToString", "castSlice", "charsStartIndex", "stringToArray", "toString", "reTrimStart", "trimStart", "string", "chars", "guard", "undefined", "replace", "strSymbols", "start", "join"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/trimStart.js"], "sourcesContent": ["import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * Removes leading whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimStart('  abc  ');\n * // => 'abc  '\n *\n * _.trimStart('-_-abc-_-', '_-');\n * // => 'abc-_-'\n */\nfunction trimStart(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.replace(reTrimStart, '');\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n      start = charsStartIndex(strSymbols, stringToArray(chars));\n\n  return castSlice(strSymbols, start).join('');\n}\n\nexport default trimStart;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,WAAW,GAAG,MAAM;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAAS,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACvCF,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;EACzB,IAAIA,MAAM,KAAKE,KAAK,IAAID,KAAK,KAAKE,SAAS,CAAC,EAAE;IAC5C,OAAOH,MAAM,CAACI,OAAO,CAACN,WAAW,EAAE,EAAE,CAAC;EACxC;EACA,IAAI,CAACE,MAAM,IAAI,EAAEC,KAAK,GAAGR,YAAY,CAACQ,KAAK,CAAC,CAAC,EAAE;IAC7C,OAAOD,MAAM;EACf;EACA,IAAIK,UAAU,GAAGT,aAAa,CAACI,MAAM,CAAC;IAClCM,KAAK,GAAGX,eAAe,CAACU,UAAU,EAAET,aAAa,CAACK,KAAK,CAAC,CAAC;EAE7D,OAAOP,SAAS,CAACW,UAAU,EAAEC,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC9C;AAEA,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}