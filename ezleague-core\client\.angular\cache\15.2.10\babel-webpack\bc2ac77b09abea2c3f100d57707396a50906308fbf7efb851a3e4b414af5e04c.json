{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar regexpTag = '[object RegExp]';\n\n/**\n * The base implementation of `_.isRegExp` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a regexp, else `false`.\n */\nfunction baseIsRegExp(value) {\n  return isObjectLike(value) && baseGetTag(value) == regexpTag;\n}\nexport default baseIsRegExp;", "map": {"version": 3, "names": ["baseGetTag", "isObjectLike", "regexpTag", "baseIsRegExp", "value"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_baseIsRegExp.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\n/** `Object#toString` result references. */\nvar regexpTag = '[object RegExp]';\n\n/**\n * The base implementation of `_.isRegExp` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a regexp, else `false`.\n */\nfunction baseIsRegExp(value) {\n  return isObjectLike(value) && baseGetTag(value) == regexpTag;\n}\n\nexport default baseIsRegExp;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,mBAAmB;;AAE5C;AACA,IAAIC,SAAS,GAAG,iBAAiB;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAY,CAACC,KAAK,EAAE;EAC3B,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIJ,UAAU,CAACI,KAAK,CAAC,IAAIF,SAAS;AAC9D;AAEA,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}