{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseUniq from './_baseUniq.js';\n\n/**\n * This method is like `_.uniq` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * uniqueness is computed. The order of result values is determined by the\n * order they occur in the array. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.uniqBy([2.1, 1.2, 2.3], Math.floor);\n * // => [2.1, 1.2]\n *\n * // The `_.property` iteratee shorthand.\n * _.uniqBy([{ 'x': 1 }, { 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }, { 'x': 2 }]\n */\nfunction uniqBy(array, iteratee) {\n  return array && array.length ? baseUniq(array, baseIteratee(iteratee, 2)) : [];\n}\nexport default uniqBy;", "map": {"version": 3, "names": ["baseIteratee", "baseUniq", "uniqBy", "array", "iteratee", "length"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/uniqBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseUniq from './_baseUniq.js';\n\n/**\n * This method is like `_.uniq` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * uniqueness is computed. The order of result values is determined by the\n * order they occur in the array. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.uniqBy([2.1, 1.2, 2.3], Math.floor);\n * // => [2.1, 1.2]\n *\n * // The `_.property` iteratee shorthand.\n * _.uniqBy([{ 'x': 1 }, { 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }, { 'x': 2 }]\n */\nfunction uniqBy(array, iteratee) {\n  return (array && array.length) ? baseUniq(array, baseIteratee(iteratee, 2)) : [];\n}\n\nexport default uniqBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAM,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC/B,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAIJ,QAAQ,CAACE,KAAK,EAAEH,YAAY,CAACI,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE;AAClF;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}