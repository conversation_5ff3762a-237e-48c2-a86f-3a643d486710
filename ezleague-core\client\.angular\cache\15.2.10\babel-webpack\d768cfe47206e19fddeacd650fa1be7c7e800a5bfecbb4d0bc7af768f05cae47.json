{"ast": null, "code": "/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n    length = props.length,\n    valsLength = values.length,\n    result = {};\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\nexport default baseZipObject;", "map": {"version": 3, "names": ["baseZipObject", "props", "values", "assignFunc", "index", "length", "vals<PERSON><PERSON><PERSON>", "result", "value", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_baseZipObject.js"], "sourcesContent": ["/**\n * This base implementation of `_.zipObject` which assigns values using `assignFunc`.\n *\n * @private\n * @param {Array} props The property identifiers.\n * @param {Array} values The property values.\n * @param {Function} assignFunc The function to assign values.\n * @returns {Object} Returns the new object.\n */\nfunction baseZipObject(props, values, assignFunc) {\n  var index = -1,\n      length = props.length,\n      valsLength = values.length,\n      result = {};\n\n  while (++index < length) {\n    var value = index < valsLength ? values[index] : undefined;\n    assignFunc(result, props[index], value);\n  }\n  return result;\n}\n\nexport default baseZipObject;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,CAACC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAE;EAChD,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,UAAU,GAAGJ,MAAM,CAACG,MAAM;IAC1BE,MAAM,GAAG,CAAC,CAAC;EAEf,OAAO,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIG,KAAK,GAAGJ,KAAK,GAAGE,UAAU,GAAGJ,MAAM,CAACE,KAAK,CAAC,GAAGK,SAAS;IAC1DN,UAAU,CAACI,MAAM,EAAEN,KAAK,CAACG,KAAK,CAAC,EAAEI,KAAK,CAAC;EACzC;EACA,OAAOD,MAAM;AACf;AAEA,eAAeP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}