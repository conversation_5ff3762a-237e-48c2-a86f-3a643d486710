{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\nconst name = \"@firebase/installations\";\nconst version = \"0.6.9\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst PENDING_TIMEOUT_MS = 10000;\nconst PACKAGE_VERSION = `w:${version}`;\nconst INTERNAL_AUTH_VERSION = 'FIS_v2';\nconst INSTALLATIONS_API_URL = 'https://firebaseinstallations.googleapis.com/v1';\nconst TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\nconst SERVICE = 'installations';\nconst SERVICE_NAME = 'Installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERROR_DESCRIPTION_MAP = {\n  [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\n  [\"not-registered\" /* ErrorCode.NOT_REGISTERED */]: 'Firebase Installation is not registered.',\n  [\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */]: 'Firebase Installation not found.',\n  [\"request-failed\" /* ErrorCode.REQUEST_FAILED */]: '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [\"app-offline\" /* ErrorCode.APP_OFFLINE */]: 'Could not process request. Application offline.',\n  [\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */]: \"Can't delete installation while there is a pending registration request.\"\n};\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nfunction isServerError(error) {\n  return error instanceof FirebaseError && error.code.includes(\"request-failed\" /* ErrorCode.REQUEST_FAILED */);\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction getInstallationsEndpoint({\n  projectId\n}) {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\nfunction extractAuthTokenInfoFromResponse(response) {\n  return {\n    token: response.token,\n    requestStatus: 2 /* RequestStatus.COMPLETED */,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\nfunction getErrorFromResponse(_x, _x2) {\n  return _getErrorFromResponse.apply(this, arguments);\n}\nfunction _getErrorFromResponse() {\n  _getErrorFromResponse = _asyncToGenerator(function* (requestName, response) {\n    const responseJson = yield response.json();\n    const errorData = responseJson.error;\n    return ERROR_FACTORY.create(\"request-failed\" /* ErrorCode.REQUEST_FAILED */, {\n      requestName,\n      serverCode: errorData.code,\n      serverMessage: errorData.message,\n      serverStatus: errorData.status\n    });\n  });\n  return _getErrorFromResponse.apply(this, arguments);\n}\nfunction getHeaders({\n  apiKey\n}) {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\nfunction getHeadersWithAuth(appConfig, {\n  refreshToken\n}) {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n/**\r\n * Calls the passed in fetch wrapper and returns the response.\r\n * If the returned response has a status of 5xx, re-runs the function once and\r\n * returns the response.\r\n */\nfunction retryIfServerError(_x3) {\n  return _retryIfServerError.apply(this, arguments);\n}\nfunction _retryIfServerError() {\n  _retryIfServerError = _asyncToGenerator(function* (fn) {\n    const result = yield fn();\n    if (result.status >= 500 && result.status < 600) {\n      // Internal Server Error. Retry request.\n      return fn();\n    }\n    return result;\n  });\n  return _retryIfServerError.apply(this, arguments);\n}\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn) {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\nfunction getAuthorizationHeader(refreshToken) {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction createInstallationRequest(_x4, _x5) {\n  return _createInstallationRequest.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/** Returns a promise that resolves after given time passes. */\nfunction _createInstallationRequest() {\n  _createInstallationRequest = _asyncToGenerator(function* ({\n    appConfig,\n    heartbeatServiceProvider\n  }, {\n    fid\n  }) {\n    const endpoint = getInstallationsEndpoint(appConfig);\n    const headers = getHeaders(appConfig);\n    // If heartbeat service exists, add the heartbeat string to the header.\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\n      optional: true\n    });\n    if (heartbeatService) {\n      const heartbeatsHeader = yield heartbeatService.getHeartbeatsHeader();\n      if (heartbeatsHeader) {\n        headers.append('x-firebase-client', heartbeatsHeader);\n      }\n    }\n    const body = {\n      fid,\n      authVersion: INTERNAL_AUTH_VERSION,\n      appId: appConfig.appId,\n      sdkVersion: PACKAGE_VERSION\n    };\n    const request = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(body)\n    };\n    const response = yield retryIfServerError(() => fetch(endpoint, request));\n    if (response.ok) {\n      const responseValue = yield response.json();\n      const registeredInstallationEntry = {\n        fid: responseValue.fid || fid,\n        registrationStatus: 2 /* RequestStatus.COMPLETED */,\n        refreshToken: responseValue.refreshToken,\n        authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n      };\n      return registeredInstallationEntry;\n    } else {\n      throw yield getErrorFromResponse('Create Installation', response);\n    }\n  });\n  return _createInstallationRequest.apply(this, arguments);\n}\nfunction sleep(ms) {\n  return new Promise(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction bufferToBase64UrlSafe(array) {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nconst INVALID_FID = '';\n/**\r\n * Generates a new FID using random values from Web Crypto API.\r\n * Returns an empty string if FID generation fails for any reason.\r\n */\nfunction generateFid() {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto = self.crypto || self.msCrypto;\n    crypto.getRandomValues(fidByteArray);\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + fidByteArray[0] % 0b00010000;\n    const fid = encode(fidByteArray);\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch (_a) {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray) {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/** Returns a string key that can be used to identify the app. */\nfunction getKey(appConfig) {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst fidChangeCallbacks = new Map();\n/**\r\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\r\n * change to other tabs.\r\n */\nfunction fidChanged(appConfig, fid) {\n  const key = getKey(appConfig);\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\nfunction addCallback(appConfig, callback) {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n  const key = getKey(appConfig);\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\nfunction removeCallback(appConfig, callback) {\n  const key = getKey(appConfig);\n  const callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    return;\n  }\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\nfunction callFidChangeCallbacks(key, fid) {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\nfunction broadcastFidChange(key, fid) {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({\n      key,\n      fid\n    });\n  }\n  closeBroadcastChannel();\n}\nlet broadcastChannel = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel() {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\nfunction closeBroadcastChannel() {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n/** Assigns or overwrites the record for the given key with the given value. */\nfunction set(_x6, _x7) {\n  return _set.apply(this, arguments);\n}\n/** Removes record(s) from the objectStore that match the given key. */\nfunction _set() {\n  _set = _asyncToGenerator(function* (appConfig, value) {\n    const key = getKey(appConfig);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n    const oldValue = yield objectStore.get(key);\n    yield objectStore.put(value, key);\n    yield tx.done;\n    if (!oldValue || oldValue.fid !== value.fid) {\n      fidChanged(appConfig, value.fid);\n    }\n    return value;\n  });\n  return _set.apply(this, arguments);\n}\nfunction remove(_x8) {\n  return _remove.apply(this, arguments);\n}\n/**\r\n * Atomically updates a record with the result of updateFn, which gets\r\n * called with the current value. If newValue is undefined, the record is\r\n * deleted instead.\r\n * @return Updated value\r\n */\nfunction _remove() {\n  _remove = _asyncToGenerator(function* (appConfig) {\n    const key = getKey(appConfig);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    yield tx.objectStore(OBJECT_STORE_NAME).delete(key);\n    yield tx.done;\n  });\n  return _remove.apply(this, arguments);\n}\nfunction update(_x9, _x10) {\n  return _update.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Updates and returns the InstallationEntry from the database.\r\n * Also triggers a registration request if it is necessary and possible.\r\n */\nfunction _update() {\n  _update = _asyncToGenerator(function* (appConfig, updateFn) {\n    const key = getKey(appConfig);\n    const db = yield getDbPromise();\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n    const store = tx.objectStore(OBJECT_STORE_NAME);\n    const oldValue = yield store.get(key);\n    const newValue = updateFn(oldValue);\n    if (newValue === undefined) {\n      yield store.delete(key);\n    } else {\n      yield store.put(newValue, key);\n    }\n    yield tx.done;\n    if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n      fidChanged(appConfig, newValue.fid);\n    }\n    return newValue;\n  });\n  return _update.apply(this, arguments);\n}\nfunction getInstallationEntry(_x11) {\n  return _getInstallationEntry.apply(this, arguments);\n}\n/**\r\n * Creates a new Installation Entry if one does not exist.\r\n * Also clears timed out pending requests.\r\n */\nfunction _getInstallationEntry() {\n  _getInstallationEntry = _asyncToGenerator(function* (installations) {\n    let registrationPromise;\n    const installationEntry = yield update(installations.appConfig, oldEntry => {\n      const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n      const entryWithPromise = triggerRegistrationIfNecessary(installations, installationEntry);\n      registrationPromise = entryWithPromise.registrationPromise;\n      return entryWithPromise.installationEntry;\n    });\n    if (installationEntry.fid === INVALID_FID) {\n      // FID generation failed. Waiting for the FID from the server.\n      return {\n        installationEntry: yield registrationPromise\n      };\n    }\n    return {\n      installationEntry,\n      registrationPromise\n    };\n  });\n  return _getInstallationEntry.apply(this, arguments);\n}\nfunction updateOrCreateInstallationEntry(oldEntry) {\n  const entry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n  };\n\n  return clearTimedOutRequest(entry);\n}\n/**\r\n * If the Firebase Installation is not registered yet, this will trigger the\r\n * registration and return an InProgressInstallationEntry.\r\n *\r\n * If registrationPromise does not exist, the installationEntry is guaranteed\r\n * to be registered.\r\n */\nfunction triggerRegistrationIfNecessary(installations, installationEntry) {\n  if (installationEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */));\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: 1 /* RequestStatus.IN_PROGRESS */,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(installations, inProgressEntry);\n    return {\n      installationEntry: inProgressEntry,\n      registrationPromise\n    };\n  } else if (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return {\n      installationEntry\n    };\n  }\n}\n/** This will be executed only once for each new Firebase Installation. */\nfunction registerInstallation(_x12, _x13) {\n  return _registerInstallation.apply(this, arguments);\n}\n/** Call if FID registration is pending in another request. */\nfunction _registerInstallation() {\n  _registerInstallation = _asyncToGenerator(function* (installations, installationEntry) {\n    try {\n      const registeredInstallationEntry = yield createInstallationRequest(installations, installationEntry);\n      return set(installations.appConfig, registeredInstallationEntry);\n    } catch (e) {\n      if (isServerError(e) && e.customData.serverCode === 409) {\n        // Server returned a \"FID cannot be used\" error.\n        // Generate a new ID next time.\n        yield remove(installations.appConfig);\n      } else {\n        // Registration failed. Set FID as not registered.\n        yield set(installations.appConfig, {\n          fid: installationEntry.fid,\n          registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n        });\n      }\n\n      throw e;\n    }\n  });\n  return _registerInstallation.apply(this, arguments);\n}\nfunction waitUntilFidRegistration(_x14) {\n  return _waitUntilFidRegistration.apply(this, arguments);\n}\n/**\r\n * Called only if there is a CreateInstallation request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * CreateInstallation request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\nfunction _waitUntilFidRegistration() {\n  _waitUntilFidRegistration = _asyncToGenerator(function* (installations) {\n    // Unfortunately, there is no way of reliably observing when a value in\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n    // so we need to poll.\n    let entry = yield updateInstallationRequest(installations.appConfig);\n    while (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n      // createInstallation request still in progress.\n      yield sleep(100);\n      entry = yield updateInstallationRequest(installations.appConfig);\n    }\n    if (entry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n      // The request timed out or failed in a different call. Try again.\n      const {\n        installationEntry,\n        registrationPromise\n      } = yield getInstallationEntry(installations);\n      if (registrationPromise) {\n        return registrationPromise;\n      } else {\n        // if there is no registrationPromise, entry is registered.\n        return installationEntry;\n      }\n    }\n    return entry;\n  });\n  return _waitUntilFidRegistration.apply(this, arguments);\n}\nfunction updateInstallationRequest(appConfig) {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */);\n    }\n\n    return clearTimedOutRequest(oldEntry);\n  });\n}\nfunction clearTimedOutRequest(entry) {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: 0 /* RequestStatus.NOT_STARTED */\n    };\n  }\n\n  return entry;\n}\nfunction hasInstallationRequestTimedOut(installationEntry) {\n  return installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */ && installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction generateAuthTokenRequest(_x15, _x16) {\n  return _generateAuthTokenRequest.apply(this, arguments);\n}\nfunction _generateAuthTokenRequest() {\n  _generateAuthTokenRequest = _asyncToGenerator(function* ({\n    appConfig,\n    heartbeatServiceProvider\n  }, installationEntry) {\n    const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\n    // If heartbeat service exists, add the heartbeat string to the header.\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\n      optional: true\n    });\n    if (heartbeatService) {\n      const heartbeatsHeader = yield heartbeatService.getHeartbeatsHeader();\n      if (heartbeatsHeader) {\n        headers.append('x-firebase-client', heartbeatsHeader);\n      }\n    }\n    const body = {\n      installation: {\n        sdkVersion: PACKAGE_VERSION,\n        appId: appConfig.appId\n      }\n    };\n    const request = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(body)\n    };\n    const response = yield retryIfServerError(() => fetch(endpoint, request));\n    if (response.ok) {\n      const responseValue = yield response.json();\n      const completedAuthToken = extractAuthTokenInfoFromResponse(responseValue);\n      return completedAuthToken;\n    } else {\n      throw yield getErrorFromResponse('Generate Auth Token', response);\n    }\n  });\n  return _generateAuthTokenRequest.apply(this, arguments);\n}\nfunction getGenerateAuthTokenEndpoint(appConfig, {\n  fid\n}) {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns a valid authentication token for the installation. Generates a new\r\n * token if one doesn't exist, is expired or about to expire.\r\n *\r\n * Should only be called if the Firebase Installation is registered.\r\n */\nfunction refreshAuthToken(_x17) {\n  return _refreshAuthToken.apply(this, arguments);\n}\n/**\r\n * Call only if FID is registered and Auth Token request is in progress.\r\n *\r\n * Waits until the current pending request finishes. If the request times out,\r\n * tries once in this thread as well.\r\n */\nfunction _refreshAuthToken() {\n  _refreshAuthToken = _asyncToGenerator(function* (installations, forceRefresh = false) {\n    let tokenPromise;\n    const entry = yield update(installations.appConfig, oldEntry => {\n      if (!isEntryRegistered(oldEntry)) {\n        throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\n      }\n\n      const oldAuthToken = oldEntry.authToken;\n      if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n        // There is a valid token in the DB.\n        return oldEntry;\n      } else if (oldAuthToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n        // There already is a token request in progress.\n        tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n        return oldEntry;\n      } else {\n        // No token or token expired.\n        if (!navigator.onLine) {\n          throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\n        }\n\n        const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n        tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n        return inProgressEntry;\n      }\n    });\n    const authToken = tokenPromise ? yield tokenPromise : entry.authToken;\n    return authToken;\n  });\n  return _refreshAuthToken.apply(this, arguments);\n}\nfunction waitUntilAuthTokenRequest(_x18, _x19) {\n  return _waitUntilAuthTokenRequest.apply(this, arguments);\n}\n/**\r\n * Called only if there is a GenerateAuthToken request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * GenerateAuthToken request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\nfunction _waitUntilAuthTokenRequest() {\n  _waitUntilAuthTokenRequest = _asyncToGenerator(function* (installations, forceRefresh) {\n    // Unfortunately, there is no way of reliably observing when a value in\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n    // so we need to poll.\n    let entry = yield updateAuthTokenRequest(installations.appConfig);\n    while (entry.authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n      // generateAuthToken still in progress.\n      yield sleep(100);\n      entry = yield updateAuthTokenRequest(installations.appConfig);\n    }\n    const authToken = entry.authToken;\n    if (authToken.requestStatus === 0 /* RequestStatus.NOT_STARTED */) {\n      // The request timed out or failed in a different call. Try again.\n      return refreshAuthToken(installations, forceRefresh);\n    } else {\n      return authToken;\n    }\n  });\n  return _waitUntilAuthTokenRequest.apply(this, arguments);\n}\nfunction updateAuthTokenRequest(appConfig) {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return Object.assign(Object.assign({}, oldEntry), {\n        authToken: {\n          requestStatus: 0 /* RequestStatus.NOT_STARTED */\n        }\n      });\n    }\n\n    return oldEntry;\n  });\n}\nfunction fetchAuthTokenFromServer(_x20, _x21) {\n  return _fetchAuthTokenFromServer.apply(this, arguments);\n}\nfunction _fetchAuthTokenFromServer() {\n  _fetchAuthTokenFromServer = _asyncToGenerator(function* (installations, installationEntry) {\n    try {\n      const authToken = yield generateAuthTokenRequest(installations, installationEntry);\n      const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), {\n        authToken\n      });\n      yield set(installations.appConfig, updatedInstallationEntry);\n      return authToken;\n    } catch (e) {\n      if (isServerError(e) && (e.customData.serverCode === 401 || e.customData.serverCode === 404)) {\n        // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n        // Generate a new ID next time.\n        yield remove(installations.appConfig);\n      } else {\n        const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), {\n          authToken: {\n            requestStatus: 0 /* RequestStatus.NOT_STARTED */\n          }\n        });\n        yield set(installations.appConfig, updatedInstallationEntry);\n      }\n      throw e;\n    }\n  });\n  return _fetchAuthTokenFromServer.apply(this, arguments);\n}\nfunction isEntryRegistered(installationEntry) {\n  return installationEntry !== undefined && installationEntry.registrationStatus === 2 /* RequestStatus.COMPLETED */;\n}\n\nfunction isAuthTokenValid(authToken) {\n  return authToken.requestStatus === 2 /* RequestStatus.COMPLETED */ && !isAuthTokenExpired(authToken);\n}\nfunction isAuthTokenExpired(authToken) {\n  const now = Date.now();\n  return now < authToken.creationTime || authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER;\n}\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(oldEntry) {\n  const inProgressAuthToken = {\n    requestStatus: 1 /* RequestStatus.IN_PROGRESS */,\n    requestTime: Date.now()\n  };\n  return Object.assign(Object.assign({}, oldEntry), {\n    authToken: inProgressAuthToken\n  });\n}\nfunction hasAuthTokenRequestTimedOut(authToken) {\n  return authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */ && authToken.requestTime + PENDING_TIMEOUT_MS < Date.now();\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Creates a Firebase Installation if there isn't one for the app and\r\n * returns the Installation ID.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\nfunction getId(_x22) {\n  return _getId.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns a Firebase Installations auth token, identifying the current\r\n * Firebase Installation.\r\n * @param installations - The `Installations` instance.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\nfunction _getId() {\n  _getId = _asyncToGenerator(function* (installations) {\n    const installationsImpl = installations;\n    const {\n      installationEntry,\n      registrationPromise\n    } = yield getInstallationEntry(installationsImpl);\n    if (registrationPromise) {\n      registrationPromise.catch(console.error);\n    } else {\n      // If the installation is already registered, update the authentication\n      // token if needed.\n      refreshAuthToken(installationsImpl).catch(console.error);\n    }\n    return installationEntry.fid;\n  });\n  return _getId.apply(this, arguments);\n}\nfunction getToken(_x23) {\n  return _getToken.apply(this, arguments);\n}\nfunction _getToken() {\n  _getToken = _asyncToGenerator(function* (installations, forceRefresh = false) {\n    const installationsImpl = installations;\n    yield completeInstallationRegistration(installationsImpl);\n    // At this point we either have a Registered Installation in the DB, or we've\n    // already thrown an error.\n    const authToken = yield refreshAuthToken(installationsImpl, forceRefresh);\n    return authToken.token;\n  });\n  return _getToken.apply(this, arguments);\n}\nfunction completeInstallationRegistration(_x24) {\n  return _completeInstallationRegistration.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _completeInstallationRegistration() {\n  _completeInstallationRegistration = _asyncToGenerator(function* (installations) {\n    const {\n      registrationPromise\n    } = yield getInstallationEntry(installations);\n    if (registrationPromise) {\n      // A createInstallation request is in progress. Wait until it finishes.\n      yield registrationPromise;\n    }\n  });\n  return _completeInstallationRegistration.apply(this, arguments);\n}\nfunction deleteInstallationRequest(_x25, _x26) {\n  return _deleteInstallationRequest.apply(this, arguments);\n}\nfunction _deleteInstallationRequest() {\n  _deleteInstallationRequest = _asyncToGenerator(function* (appConfig, installationEntry) {\n    const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\n    const request = {\n      method: 'DELETE',\n      headers\n    };\n    const response = yield retryIfServerError(() => fetch(endpoint, request));\n    if (!response.ok) {\n      throw yield getErrorFromResponse('Delete Installation', response);\n    }\n  });\n  return _deleteInstallationRequest.apply(this, arguments);\n}\nfunction getDeleteEndpoint(appConfig, {\n  fid\n}) {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Deletes the Firebase Installation and all associated data.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\nfunction deleteInstallations(_x27) {\n  return _deleteInstallations.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Sets a new callback that will get called when Installation ID changes.\r\n * Returns an unsubscribe function that will remove the callback when called.\r\n * @param installations - The `Installations` instance.\r\n * @param callback - The callback function that is invoked when FID changes.\r\n * @returns A function that can be called to unsubscribe.\r\n *\r\n * @public\r\n */\nfunction _deleteInstallations() {\n  _deleteInstallations = _asyncToGenerator(function* (installations) {\n    const {\n      appConfig\n    } = installations;\n    const entry = yield update(appConfig, oldEntry => {\n      if (oldEntry && oldEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\n        // Delete the unregistered entry without sending a deleteInstallation request.\n        return undefined;\n      }\n      return oldEntry;\n    });\n    if (entry) {\n      if (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\n        // Can't delete while trying to register.\n        throw ERROR_FACTORY.create(\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */);\n      } else if (entry.registrationStatus === 2 /* RequestStatus.COMPLETED */) {\n        if (!navigator.onLine) {\n          throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\n        } else {\n          yield deleteInstallationRequest(appConfig, entry);\n          yield remove(appConfig);\n        }\n      }\n    }\n  });\n  return _deleteInstallations.apply(this, arguments);\n}\nfunction onIdChange(installations, callback) {\n  const {\n    appConfig\n  } = installations;\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Returns an instance of {@link Installations} associated with the given\r\n * {@link @firebase/app#FirebaseApp} instance.\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * @public\r\n */\nfunction getInstallations(app = getApp()) {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction extractAppConfig(app) {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n  // Required app config keys\n  const configKeys = ['projectId', 'apiKey', 'appId'];\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n  return {\n    appName: app.name,\n    projectId: app.options.projectId,\n    apiKey: app.options.apiKey,\n    appId: app.options.appId\n  };\n}\nfunction getMissingValueError(valueName) {\n  return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\n    valueName\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\nconst publicFactory = container => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n  const installationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\nconst internalFactory = container => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n  const installationsInternal = {\n    getId: () => getId(installations),\n    getToken: forceRefresh => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\nfunction registerInstallations() {\n  _registerComponent(new Component(INSTALLATIONS_NAME, publicFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  _registerComponent(new Component(INSTALLATIONS_NAME_INTERNAL, internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n}\n\n/**\r\n * The Firebase Installations Web SDK.\r\n * This SDK does not work in a Node.js environment.\r\n *\r\n * @packageDocumentation\r\n */\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\nregisterVersion(name, version, 'esm2017');\nexport { deleteInstallations, getId, getInstallations, getToken, onIdChange };", "map": {"version": 3, "names": ["_get<PERSON><PERSON><PERSON>", "getApp", "_registerComponent", "registerVersion", "Component", "ErrorFactory", "FirebaseError", "openDB", "name", "version", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "SERVICE", "SERVICE_NAME", "ERROR_DESCRIPTION_MAP", "ERROR_FACTORY", "isServerError", "error", "code", "includes", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "getExpiresInFromResponseExpiresIn", "creationTime", "Date", "now", "getErrorFromResponse", "requestName", "responseJson", "json", "errorData", "create", "serverCode", "serverMessage", "message", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "fn", "result", "responseExpiresIn", "Number", "replace", "createInstallationRequest", "heartbeatServiceProvider", "fid", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "appId", "sdkVersion", "request", "method", "JSON", "stringify", "fetch", "ok", "responseValue", "registeredInstallationEntry", "registrationStatus", "authToken", "sleep", "ms", "Promise", "resolve", "setTimeout", "bufferToBase64UrlSafe", "array", "b64", "btoa", "String", "fromCharCode", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "crypto", "self", "msCrypto", "getRandomValues", "encode", "test", "_a", "b64String", "substr", "<PERSON><PERSON><PERSON>", "appName", "fidChangeCallbacks", "Map", "fidChanged", "key", "callFidChangeCallbacks", "broadcastFidChange", "addCallback", "callback", "getBroadcastChannel", "callbackSet", "get", "Set", "set", "add", "removeCallback", "delete", "size", "closeBroadcastChannel", "callbacks", "channel", "postMessage", "broadcastChannel", "BroadcastChannel", "onmessage", "e", "data", "close", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgrade", "db", "oldVersion", "createObjectStore", "value", "tx", "transaction", "objectStore", "oldValue", "put", "done", "remove", "update", "updateFn", "store", "newValue", "undefined", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "entryWithPromise", "triggerRegistrationIfNecessary", "entry", "clearTimedOutRequest", "navigator", "onLine", "registrationPromiseWithError", "reject", "inProgressEntry", "registrationTime", "registerInstallation", "waitUntilFidRegistration", "customData", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "completedAuthToken", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "waitUntilAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "fetchAuthTokenFromServer", "updateAuthTokenRequest", "hasAuthTokenRequestTimedOut", "Object", "assign", "updatedInstallationEntry", "isAuthTokenExpired", "inProgressAuthToken", "requestTime", "getId", "installationsImpl", "catch", "console", "getToken", "completeInstallationRegistration", "deleteInstallationRequest", "getDeleteEndpoint", "deleteInstallations", "onIdChange", "getInstallations", "app", "extractAppConfig", "options", "getMissingValueError", "config<PERSON><PERSON><PERSON>", "keyName", "valueName", "INSTALLATIONS_NAME", "INSTALLATIONS_NAME_INTERNAL", "publicFactory", "container", "get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "installationsInternal", "registerInstallations"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/@firebase/installations/dist/esm/index.esm2017.js"], "sourcesContent": ["import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\nconst name = \"@firebase/installations\";\nconst version = \"0.6.9\";\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst PENDING_TIMEOUT_MS = 10000;\r\nconst PACKAGE_VERSION = `w:${version}`;\r\nconst INTERNAL_AUTH_VERSION = 'FIS_v2';\r\nconst INSTALLATIONS_API_URL = 'https://firebaseinstallations.googleapis.com/v1';\r\nconst TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\r\nconst SERVICE = 'installations';\r\nconst SERVICE_NAME = 'Installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERROR_DESCRIPTION_MAP = {\r\n    [\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */]: 'Missing App configuration value: \"{$valueName}\"',\r\n    [\"not-registered\" /* ErrorCode.NOT_REGISTERED */]: 'Firebase Installation is not registered.',\r\n    [\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */]: 'Firebase Installation not found.',\r\n    [\"request-failed\" /* ErrorCode.REQUEST_FAILED */]: '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\r\n    [\"app-offline\" /* ErrorCode.APP_OFFLINE */]: 'Could not process request. Application offline.',\r\n    [\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */]: \"Can't delete installation while there is a pending registration request.\"\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory(SERVICE, SERVICE_NAME, ERROR_DESCRIPTION_MAP);\r\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\r\nfunction isServerError(error) {\r\n    return (error instanceof FirebaseError &&\r\n        error.code.includes(\"request-failed\" /* ErrorCode.REQUEST_FAILED */));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getInstallationsEndpoint({ projectId }) {\r\n    return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\r\n}\r\nfunction extractAuthTokenInfoFromResponse(response) {\r\n    return {\r\n        token: response.token,\r\n        requestStatus: 2 /* RequestStatus.COMPLETED */,\r\n        expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\r\n        creationTime: Date.now()\r\n    };\r\n}\r\nasync function getErrorFromResponse(requestName, response) {\r\n    const responseJson = await response.json();\r\n    const errorData = responseJson.error;\r\n    return ERROR_FACTORY.create(\"request-failed\" /* ErrorCode.REQUEST_FAILED */, {\r\n        requestName,\r\n        serverCode: errorData.code,\r\n        serverMessage: errorData.message,\r\n        serverStatus: errorData.status\r\n    });\r\n}\r\nfunction getHeaders({ apiKey }) {\r\n    return new Headers({\r\n        'Content-Type': 'application/json',\r\n        Accept: 'application/json',\r\n        'x-goog-api-key': apiKey\r\n    });\r\n}\r\nfunction getHeadersWithAuth(appConfig, { refreshToken }) {\r\n    const headers = getHeaders(appConfig);\r\n    headers.append('Authorization', getAuthorizationHeader(refreshToken));\r\n    return headers;\r\n}\r\n/**\r\n * Calls the passed in fetch wrapper and returns the response.\r\n * If the returned response has a status of 5xx, re-runs the function once and\r\n * returns the response.\r\n */\r\nasync function retryIfServerError(fn) {\r\n    const result = await fn();\r\n    if (result.status >= 500 && result.status < 600) {\r\n        // Internal Server Error. Retry request.\r\n        return fn();\r\n    }\r\n    return result;\r\n}\r\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn) {\r\n    // This works because the server will never respond with fractions of a second.\r\n    return Number(responseExpiresIn.replace('s', '000'));\r\n}\r\nfunction getAuthorizationHeader(refreshToken) {\r\n    return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function createInstallationRequest({ appConfig, heartbeatServiceProvider }, { fid }) {\r\n    const endpoint = getInstallationsEndpoint(appConfig);\r\n    const headers = getHeaders(appConfig);\r\n    // If heartbeat service exists, add the heartbeat string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers.append('x-firebase-client', heartbeatsHeader);\r\n        }\r\n    }\r\n    const body = {\r\n        fid,\r\n        authVersion: INTERNAL_AUTH_VERSION,\r\n        appId: appConfig.appId,\r\n        sdkVersion: PACKAGE_VERSION\r\n    };\r\n    const request = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (response.ok) {\r\n        const responseValue = await response.json();\r\n        const registeredInstallationEntry = {\r\n            fid: responseValue.fid || fid,\r\n            registrationStatus: 2 /* RequestStatus.COMPLETED */,\r\n            refreshToken: responseValue.refreshToken,\r\n            authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\r\n        };\r\n        return registeredInstallationEntry;\r\n    }\r\n    else {\r\n        throw await getErrorFromResponse('Create Installation', response);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a promise that resolves after given time passes. */\r\nfunction sleep(ms) {\r\n    return new Promise(resolve => {\r\n        setTimeout(resolve, ms);\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction bufferToBase64UrlSafe(array) {\r\n    const b64 = btoa(String.fromCharCode(...array));\r\n    return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\r\nconst INVALID_FID = '';\r\n/**\r\n * Generates a new FID using random values from Web Crypto API.\r\n * Returns an empty string if FID generation fails for any reason.\r\n */\r\nfunction generateFid() {\r\n    try {\r\n        // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\r\n        // bytes. our implementation generates a 17 byte array instead.\r\n        const fidByteArray = new Uint8Array(17);\r\n        const crypto = self.crypto || self.msCrypto;\r\n        crypto.getRandomValues(fidByteArray);\r\n        // Replace the first 4 random bits with the constant FID header of 0b0111.\r\n        fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\r\n        const fid = encode(fidByteArray);\r\n        return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\r\n    }\r\n    catch (_a) {\r\n        // FID generation errored\r\n        return INVALID_FID;\r\n    }\r\n}\r\n/** Converts a FID Uint8Array to a base64 string representation. */\r\nfunction encode(fidByteArray) {\r\n    const b64String = bufferToBase64UrlSafe(fidByteArray);\r\n    // Remove the 23rd character that was added because of the extra 4 bits at the\r\n    // end of our 17 byte array, and the '=' padding.\r\n    return b64String.substr(0, 22);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Returns a string key that can be used to identify the app. */\r\nfunction getKey(appConfig) {\r\n    return `${appConfig.appName}!${appConfig.appId}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst fidChangeCallbacks = new Map();\r\n/**\r\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\r\n * change to other tabs.\r\n */\r\nfunction fidChanged(appConfig, fid) {\r\n    const key = getKey(appConfig);\r\n    callFidChangeCallbacks(key, fid);\r\n    broadcastFidChange(key, fid);\r\n}\r\nfunction addCallback(appConfig, callback) {\r\n    // Open the broadcast channel if it's not already open,\r\n    // to be able to listen to change events from other tabs.\r\n    getBroadcastChannel();\r\n    const key = getKey(appConfig);\r\n    let callbackSet = fidChangeCallbacks.get(key);\r\n    if (!callbackSet) {\r\n        callbackSet = new Set();\r\n        fidChangeCallbacks.set(key, callbackSet);\r\n    }\r\n    callbackSet.add(callback);\r\n}\r\nfunction removeCallback(appConfig, callback) {\r\n    const key = getKey(appConfig);\r\n    const callbackSet = fidChangeCallbacks.get(key);\r\n    if (!callbackSet) {\r\n        return;\r\n    }\r\n    callbackSet.delete(callback);\r\n    if (callbackSet.size === 0) {\r\n        fidChangeCallbacks.delete(key);\r\n    }\r\n    // Close broadcast channel if there are no more callbacks.\r\n    closeBroadcastChannel();\r\n}\r\nfunction callFidChangeCallbacks(key, fid) {\r\n    const callbacks = fidChangeCallbacks.get(key);\r\n    if (!callbacks) {\r\n        return;\r\n    }\r\n    for (const callback of callbacks) {\r\n        callback(fid);\r\n    }\r\n}\r\nfunction broadcastFidChange(key, fid) {\r\n    const channel = getBroadcastChannel();\r\n    if (channel) {\r\n        channel.postMessage({ key, fid });\r\n    }\r\n    closeBroadcastChannel();\r\n}\r\nlet broadcastChannel = null;\r\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\r\nfunction getBroadcastChannel() {\r\n    if (!broadcastChannel && 'BroadcastChannel' in self) {\r\n        broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\r\n        broadcastChannel.onmessage = e => {\r\n            callFidChangeCallbacks(e.data.key, e.data.fid);\r\n        };\r\n    }\r\n    return broadcastChannel;\r\n}\r\nfunction closeBroadcastChannel() {\r\n    if (fidChangeCallbacks.size === 0 && broadcastChannel) {\r\n        broadcastChannel.close();\r\n        broadcastChannel = null;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DATABASE_NAME = 'firebase-installations-database';\r\nconst DATABASE_VERSION = 1;\r\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\r\nlet dbPromise = null;\r\nfunction getDbPromise() {\r\n    if (!dbPromise) {\r\n        dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\r\n            upgrade: (db, oldVersion) => {\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (oldVersion) {\r\n                    case 0:\r\n                        db.createObjectStore(OBJECT_STORE_NAME);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return dbPromise;\r\n}\r\n/** Assigns or overwrites the record for the given key with the given value. */\r\nasync function set(appConfig, value) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    const objectStore = tx.objectStore(OBJECT_STORE_NAME);\r\n    const oldValue = (await objectStore.get(key));\r\n    await objectStore.put(value, key);\r\n    await tx.done;\r\n    if (!oldValue || oldValue.fid !== value.fid) {\r\n        fidChanged(appConfig, value.fid);\r\n    }\r\n    return value;\r\n}\r\n/** Removes record(s) from the objectStore that match the given key. */\r\nasync function remove(appConfig) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    await tx.objectStore(OBJECT_STORE_NAME).delete(key);\r\n    await tx.done;\r\n}\r\n/**\r\n * Atomically updates a record with the result of updateFn, which gets\r\n * called with the current value. If newValue is undefined, the record is\r\n * deleted instead.\r\n * @return Updated value\r\n */\r\nasync function update(appConfig, updateFn) {\r\n    const key = getKey(appConfig);\r\n    const db = await getDbPromise();\r\n    const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\r\n    const store = tx.objectStore(OBJECT_STORE_NAME);\r\n    const oldValue = (await store.get(key));\r\n    const newValue = updateFn(oldValue);\r\n    if (newValue === undefined) {\r\n        await store.delete(key);\r\n    }\r\n    else {\r\n        await store.put(newValue, key);\r\n    }\r\n    await tx.done;\r\n    if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\r\n        fidChanged(appConfig, newValue.fid);\r\n    }\r\n    return newValue;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Updates and returns the InstallationEntry from the database.\r\n * Also triggers a registration request if it is necessary and possible.\r\n */\r\nasync function getInstallationEntry(installations) {\r\n    let registrationPromise;\r\n    const installationEntry = await update(installations.appConfig, oldEntry => {\r\n        const installationEntry = updateOrCreateInstallationEntry(oldEntry);\r\n        const entryWithPromise = triggerRegistrationIfNecessary(installations, installationEntry);\r\n        registrationPromise = entryWithPromise.registrationPromise;\r\n        return entryWithPromise.installationEntry;\r\n    });\r\n    if (installationEntry.fid === INVALID_FID) {\r\n        // FID generation failed. Waiting for the FID from the server.\r\n        return { installationEntry: await registrationPromise };\r\n    }\r\n    return {\r\n        installationEntry,\r\n        registrationPromise\r\n    };\r\n}\r\n/**\r\n * Creates a new Installation Entry if one does not exist.\r\n * Also clears timed out pending requests.\r\n */\r\nfunction updateOrCreateInstallationEntry(oldEntry) {\r\n    const entry = oldEntry || {\r\n        fid: generateFid(),\r\n        registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n    };\r\n    return clearTimedOutRequest(entry);\r\n}\r\n/**\r\n * If the Firebase Installation is not registered yet, this will trigger the\r\n * registration and return an InProgressInstallationEntry.\r\n *\r\n * If registrationPromise does not exist, the installationEntry is guaranteed\r\n * to be registered.\r\n */\r\nfunction triggerRegistrationIfNecessary(installations, installationEntry) {\r\n    if (installationEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        if (!navigator.onLine) {\r\n            // Registration required but app is offline.\r\n            const registrationPromiseWithError = Promise.reject(ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */));\r\n            return {\r\n                installationEntry,\r\n                registrationPromise: registrationPromiseWithError\r\n            };\r\n        }\r\n        // Try registering. Change status to IN_PROGRESS.\r\n        const inProgressEntry = {\r\n            fid: installationEntry.fid,\r\n            registrationStatus: 1 /* RequestStatus.IN_PROGRESS */,\r\n            registrationTime: Date.now()\r\n        };\r\n        const registrationPromise = registerInstallation(installations, inProgressEntry);\r\n        return { installationEntry: inProgressEntry, registrationPromise };\r\n    }\r\n    else if (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        return {\r\n            installationEntry,\r\n            registrationPromise: waitUntilFidRegistration(installations)\r\n        };\r\n    }\r\n    else {\r\n        return { installationEntry };\r\n    }\r\n}\r\n/** This will be executed only once for each new Firebase Installation. */\r\nasync function registerInstallation(installations, installationEntry) {\r\n    try {\r\n        const registeredInstallationEntry = await createInstallationRequest(installations, installationEntry);\r\n        return set(installations.appConfig, registeredInstallationEntry);\r\n    }\r\n    catch (e) {\r\n        if (isServerError(e) && e.customData.serverCode === 409) {\r\n            // Server returned a \"FID cannot be used\" error.\r\n            // Generate a new ID next time.\r\n            await remove(installations.appConfig);\r\n        }\r\n        else {\r\n            // Registration failed. Set FID as not registered.\r\n            await set(installations.appConfig, {\r\n                fid: installationEntry.fid,\r\n                registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n            });\r\n        }\r\n        throw e;\r\n    }\r\n}\r\n/** Call if FID registration is pending in another request. */\r\nasync function waitUntilFidRegistration(installations) {\r\n    // Unfortunately, there is no way of reliably observing when a value in\r\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\r\n    // so we need to poll.\r\n    let entry = await updateInstallationRequest(installations.appConfig);\r\n    while (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        // createInstallation request still in progress.\r\n        await sleep(100);\r\n        entry = await updateInstallationRequest(installations.appConfig);\r\n    }\r\n    if (entry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        // The request timed out or failed in a different call. Try again.\r\n        const { installationEntry, registrationPromise } = await getInstallationEntry(installations);\r\n        if (registrationPromise) {\r\n            return registrationPromise;\r\n        }\r\n        else {\r\n            // if there is no registrationPromise, entry is registered.\r\n            return installationEntry;\r\n        }\r\n    }\r\n    return entry;\r\n}\r\n/**\r\n * Called only if there is a CreateInstallation request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * CreateInstallation request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\r\nfunction updateInstallationRequest(appConfig) {\r\n    return update(appConfig, oldEntry => {\r\n        if (!oldEntry) {\r\n            throw ERROR_FACTORY.create(\"installation-not-found\" /* ErrorCode.INSTALLATION_NOT_FOUND */);\r\n        }\r\n        return clearTimedOutRequest(oldEntry);\r\n    });\r\n}\r\nfunction clearTimedOutRequest(entry) {\r\n    if (hasInstallationRequestTimedOut(entry)) {\r\n        return {\r\n            fid: entry.fid,\r\n            registrationStatus: 0 /* RequestStatus.NOT_STARTED */\r\n        };\r\n    }\r\n    return entry;\r\n}\r\nfunction hasInstallationRequestTimedOut(installationEntry) {\r\n    return (installationEntry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */ &&\r\n        installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now());\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function generateAuthTokenRequest({ appConfig, heartbeatServiceProvider }, installationEntry) {\r\n    const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\r\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\r\n    // If heartbeat service exists, add the heartbeat string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers.append('x-firebase-client', heartbeatsHeader);\r\n        }\r\n    }\r\n    const body = {\r\n        installation: {\r\n            sdkVersion: PACKAGE_VERSION,\r\n            appId: appConfig.appId\r\n        }\r\n    };\r\n    const request = {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify(body)\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (response.ok) {\r\n        const responseValue = await response.json();\r\n        const completedAuthToken = extractAuthTokenInfoFromResponse(responseValue);\r\n        return completedAuthToken;\r\n    }\r\n    else {\r\n        throw await getErrorFromResponse('Generate Auth Token', response);\r\n    }\r\n}\r\nfunction getGenerateAuthTokenEndpoint(appConfig, { fid }) {\r\n    return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a valid authentication token for the installation. Generates a new\r\n * token if one doesn't exist, is expired or about to expire.\r\n *\r\n * Should only be called if the Firebase Installation is registered.\r\n */\r\nasync function refreshAuthToken(installations, forceRefresh = false) {\r\n    let tokenPromise;\r\n    const entry = await update(installations.appConfig, oldEntry => {\r\n        if (!isEntryRegistered(oldEntry)) {\r\n            throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\r\n        }\r\n        const oldAuthToken = oldEntry.authToken;\r\n        if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\r\n            // There is a valid token in the DB.\r\n            return oldEntry;\r\n        }\r\n        else if (oldAuthToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n            // There already is a token request in progress.\r\n            tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\r\n            return oldEntry;\r\n        }\r\n        else {\r\n            // No token or token expired.\r\n            if (!navigator.onLine) {\r\n                throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\r\n            }\r\n            const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\r\n            tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\r\n            return inProgressEntry;\r\n        }\r\n    });\r\n    const authToken = tokenPromise\r\n        ? await tokenPromise\r\n        : entry.authToken;\r\n    return authToken;\r\n}\r\n/**\r\n * Call only if FID is registered and Auth Token request is in progress.\r\n *\r\n * Waits until the current pending request finishes. If the request times out,\r\n * tries once in this thread as well.\r\n */\r\nasync function waitUntilAuthTokenRequest(installations, forceRefresh) {\r\n    // Unfortunately, there is no way of reliably observing when a value in\r\n    // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\r\n    // so we need to poll.\r\n    let entry = await updateAuthTokenRequest(installations.appConfig);\r\n    while (entry.authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n        // generateAuthToken still in progress.\r\n        await sleep(100);\r\n        entry = await updateAuthTokenRequest(installations.appConfig);\r\n    }\r\n    const authToken = entry.authToken;\r\n    if (authToken.requestStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n        // The request timed out or failed in a different call. Try again.\r\n        return refreshAuthToken(installations, forceRefresh);\r\n    }\r\n    else {\r\n        return authToken;\r\n    }\r\n}\r\n/**\r\n * Called only if there is a GenerateAuthToken request in progress.\r\n *\r\n * Updates the InstallationEntry in the DB based on the status of the\r\n * GenerateAuthToken request.\r\n *\r\n * Returns the updated InstallationEntry.\r\n */\r\nfunction updateAuthTokenRequest(appConfig) {\r\n    return update(appConfig, oldEntry => {\r\n        if (!isEntryRegistered(oldEntry)) {\r\n            throw ERROR_FACTORY.create(\"not-registered\" /* ErrorCode.NOT_REGISTERED */);\r\n        }\r\n        const oldAuthToken = oldEntry.authToken;\r\n        if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\r\n            return Object.assign(Object.assign({}, oldEntry), { authToken: { requestStatus: 0 /* RequestStatus.NOT_STARTED */ } });\r\n        }\r\n        return oldEntry;\r\n    });\r\n}\r\nasync function fetchAuthTokenFromServer(installations, installationEntry) {\r\n    try {\r\n        const authToken = await generateAuthTokenRequest(installations, installationEntry);\r\n        const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), { authToken });\r\n        await set(installations.appConfig, updatedInstallationEntry);\r\n        return authToken;\r\n    }\r\n    catch (e) {\r\n        if (isServerError(e) &&\r\n            (e.customData.serverCode === 401 || e.customData.serverCode === 404)) {\r\n            // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\r\n            // Generate a new ID next time.\r\n            await remove(installations.appConfig);\r\n        }\r\n        else {\r\n            const updatedInstallationEntry = Object.assign(Object.assign({}, installationEntry), { authToken: { requestStatus: 0 /* RequestStatus.NOT_STARTED */ } });\r\n            await set(installations.appConfig, updatedInstallationEntry);\r\n        }\r\n        throw e;\r\n    }\r\n}\r\nfunction isEntryRegistered(installationEntry) {\r\n    return (installationEntry !== undefined &&\r\n        installationEntry.registrationStatus === 2 /* RequestStatus.COMPLETED */);\r\n}\r\nfunction isAuthTokenValid(authToken) {\r\n    return (authToken.requestStatus === 2 /* RequestStatus.COMPLETED */ &&\r\n        !isAuthTokenExpired(authToken));\r\n}\r\nfunction isAuthTokenExpired(authToken) {\r\n    const now = Date.now();\r\n    return (now < authToken.creationTime ||\r\n        authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER);\r\n}\r\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\r\nfunction makeAuthTokenRequestInProgressEntry(oldEntry) {\r\n    const inProgressAuthToken = {\r\n        requestStatus: 1 /* RequestStatus.IN_PROGRESS */,\r\n        requestTime: Date.now()\r\n    };\r\n    return Object.assign(Object.assign({}, oldEntry), { authToken: inProgressAuthToken });\r\n}\r\nfunction hasAuthTokenRequestTimedOut(authToken) {\r\n    return (authToken.requestStatus === 1 /* RequestStatus.IN_PROGRESS */ &&\r\n        authToken.requestTime + PENDING_TIMEOUT_MS < Date.now());\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Creates a Firebase Installation if there isn't one for the app and\r\n * returns the Installation ID.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\r\nasync function getId(installations) {\r\n    const installationsImpl = installations;\r\n    const { installationEntry, registrationPromise } = await getInstallationEntry(installationsImpl);\r\n    if (registrationPromise) {\r\n        registrationPromise.catch(console.error);\r\n    }\r\n    else {\r\n        // If the installation is already registered, update the authentication\r\n        // token if needed.\r\n        refreshAuthToken(installationsImpl).catch(console.error);\r\n    }\r\n    return installationEntry.fid;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a Firebase Installations auth token, identifying the current\r\n * Firebase Installation.\r\n * @param installations - The `Installations` instance.\r\n * @param forceRefresh - Force refresh regardless of token expiration.\r\n *\r\n * @public\r\n */\r\nasync function getToken(installations, forceRefresh = false) {\r\n    const installationsImpl = installations;\r\n    await completeInstallationRegistration(installationsImpl);\r\n    // At this point we either have a Registered Installation in the DB, or we've\r\n    // already thrown an error.\r\n    const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\r\n    return authToken.token;\r\n}\r\nasync function completeInstallationRegistration(installations) {\r\n    const { registrationPromise } = await getInstallationEntry(installations);\r\n    if (registrationPromise) {\r\n        // A createInstallation request is in progress. Wait until it finishes.\r\n        await registrationPromise;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function deleteInstallationRequest(appConfig, installationEntry) {\r\n    const endpoint = getDeleteEndpoint(appConfig, installationEntry);\r\n    const headers = getHeadersWithAuth(appConfig, installationEntry);\r\n    const request = {\r\n        method: 'DELETE',\r\n        headers\r\n    };\r\n    const response = await retryIfServerError(() => fetch(endpoint, request));\r\n    if (!response.ok) {\r\n        throw await getErrorFromResponse('Delete Installation', response);\r\n    }\r\n}\r\nfunction getDeleteEndpoint(appConfig, { fid }) {\r\n    return `${getInstallationsEndpoint(appConfig)}/${fid}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Deletes the Firebase Installation and all associated data.\r\n * @param installations - The `Installations` instance.\r\n *\r\n * @public\r\n */\r\nasync function deleteInstallations(installations) {\r\n    const { appConfig } = installations;\r\n    const entry = await update(appConfig, oldEntry => {\r\n        if (oldEntry && oldEntry.registrationStatus === 0 /* RequestStatus.NOT_STARTED */) {\r\n            // Delete the unregistered entry without sending a deleteInstallation request.\r\n            return undefined;\r\n        }\r\n        return oldEntry;\r\n    });\r\n    if (entry) {\r\n        if (entry.registrationStatus === 1 /* RequestStatus.IN_PROGRESS */) {\r\n            // Can't delete while trying to register.\r\n            throw ERROR_FACTORY.create(\"delete-pending-registration\" /* ErrorCode.DELETE_PENDING_REGISTRATION */);\r\n        }\r\n        else if (entry.registrationStatus === 2 /* RequestStatus.COMPLETED */) {\r\n            if (!navigator.onLine) {\r\n                throw ERROR_FACTORY.create(\"app-offline\" /* ErrorCode.APP_OFFLINE */);\r\n            }\r\n            else {\r\n                await deleteInstallationRequest(appConfig, entry);\r\n                await remove(appConfig);\r\n            }\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Sets a new callback that will get called when Installation ID changes.\r\n * Returns an unsubscribe function that will remove the callback when called.\r\n * @param installations - The `Installations` instance.\r\n * @param callback - The callback function that is invoked when FID changes.\r\n * @returns A function that can be called to unsubscribe.\r\n *\r\n * @public\r\n */\r\nfunction onIdChange(installations, callback) {\r\n    const { appConfig } = installations;\r\n    addCallback(appConfig, callback);\r\n    return () => {\r\n        removeCallback(appConfig, callback);\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns an instance of {@link Installations} associated with the given\r\n * {@link @firebase/app#FirebaseApp} instance.\r\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\r\n *\r\n * @public\r\n */\r\nfunction getInstallations(app = getApp()) {\r\n    const installationsImpl = _getProvider(app, 'installations').getImmediate();\r\n    return installationsImpl;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction extractAppConfig(app) {\r\n    if (!app || !app.options) {\r\n        throw getMissingValueError('App Configuration');\r\n    }\r\n    if (!app.name) {\r\n        throw getMissingValueError('App Name');\r\n    }\r\n    // Required app config keys\r\n    const configKeys = [\r\n        'projectId',\r\n        'apiKey',\r\n        'appId'\r\n    ];\r\n    for (const keyName of configKeys) {\r\n        if (!app.options[keyName]) {\r\n            throw getMissingValueError(keyName);\r\n        }\r\n    }\r\n    return {\r\n        appName: app.name,\r\n        projectId: app.options.projectId,\r\n        apiKey: app.options.apiKey,\r\n        appId: app.options.appId\r\n    };\r\n}\r\nfunction getMissingValueError(valueName) {\r\n    return ERROR_FACTORY.create(\"missing-app-config-values\" /* ErrorCode.MISSING_APP_CONFIG_VALUES */, {\r\n        valueName\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst INSTALLATIONS_NAME = 'installations';\r\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\r\nconst publicFactory = (container) => {\r\n    const app = container.getProvider('app').getImmediate();\r\n    // Throws if app isn't configured properly.\r\n    const appConfig = extractAppConfig(app);\r\n    const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\r\n    const installationsImpl = {\r\n        app,\r\n        appConfig,\r\n        heartbeatServiceProvider,\r\n        _delete: () => Promise.resolve()\r\n    };\r\n    return installationsImpl;\r\n};\r\nconst internalFactory = (container) => {\r\n    const app = container.getProvider('app').getImmediate();\r\n    // Internal FIS instance relies on public FIS instance.\r\n    const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\r\n    const installationsInternal = {\r\n        getId: () => getId(installations),\r\n        getToken: (forceRefresh) => getToken(installations, forceRefresh)\r\n    };\r\n    return installationsInternal;\r\n};\r\nfunction registerInstallations() {\r\n    _registerComponent(new Component(INSTALLATIONS_NAME, publicFactory, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    _registerComponent(new Component(INSTALLATIONS_NAME_INTERNAL, internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n}\n\n/**\r\n * The Firebase Installations Web SDK.\r\n * This SDK does not work in a Node.js environment.\r\n *\r\n * @packageDocumentation\r\n */\r\nregisterInstallations();\r\nregisterVersion(name, version);\r\n// BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\nregisterVersion(name, version, 'esm2017');\n\nexport { deleteInstallations, getId, getInstallations, getToken, onIdChange };\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,eAAe;AACzF,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,YAAY,EAAEC,aAAa,QAAQ,gBAAgB;AAC5D,SAASC,MAAM,QAAQ,KAAK;AAE5B,MAAMC,IAAI,GAAG,yBAAyB;AACtC,MAAMC,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,KAAK;AAChC,MAAMC,eAAe,GAAI,KAAIF,OAAQ,EAAC;AACtC,MAAMG,qBAAqB,GAAG,QAAQ;AACtC,MAAMC,qBAAqB,GAAG,iDAAiD;AAC/E,MAAMC,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAChD,MAAMC,OAAO,GAAG,eAAe;AAC/B,MAAMC,YAAY,GAAG,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;EAC1B,CAAC,2BAA2B,CAAC,4CAA4C,iDAAiD;EAC1H,CAAC,gBAAgB,CAAC,iCAAiC,0CAA0C;EAC7F,CAAC,wBAAwB,CAAC,yCAAyC,kCAAkC;EACrG,CAAC,gBAAgB,CAAC,iCAAiC,4FAA4F;EAC/I,CAAC,aAAa,CAAC,8BAA8B,iDAAiD;EAC9F,CAAC,6BAA6B,CAAC,8CAA8C;AACjF,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIb,YAAY,CAACU,OAAO,EAAEC,YAAY,EAAEC,qBAAqB,CAAC;AACpF;AACA,SAASE,aAAa,CAACC,KAAK,EAAE;EAC1B,OAAQA,KAAK,YAAYd,aAAa,IAClCc,KAAK,CAACC,IAAI,CAACC,QAAQ,CAAC,gBAAgB,CAAC,+BAA+B;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwB,CAAC;EAAEC;AAAU,CAAC,EAAE;EAC7C,OAAQ,GAAEX,qBAAsB,aAAYW,SAAU,gBAAe;AACzE;AACA,SAASC,gCAAgC,CAACC,QAAQ,EAAE;EAChD,OAAO;IACHC,KAAK,EAAED,QAAQ,CAACC,KAAK;IACrBC,aAAa,EAAE,CAAC,CAAC;IACjBC,SAAS,EAAEC,iCAAiC,CAACJ,QAAQ,CAACG,SAAS,CAAC;IAChEE,YAAY,EAAEC,IAAI,CAACC,GAAG;EAC1B,CAAC;AACL;AAAC,SACcC,oBAAoB;EAAA;AAAA;AAAA;EAAA,0CAAnC,WAAoCC,WAAW,EAAET,QAAQ,EAAE;IACvD,MAAMU,YAAY,SAASV,QAAQ,CAACW,IAAI,EAAE;IAC1C,MAAMC,SAAS,GAAGF,YAAY,CAAChB,KAAK;IACpC,OAAOF,aAAa,CAACqB,MAAM,CAAC,gBAAgB,CAAC,gCAAgC;MACzEJ,WAAW;MACXK,UAAU,EAAEF,SAAS,CAACjB,IAAI;MAC1BoB,aAAa,EAAEH,SAAS,CAACI,OAAO;MAChCC,YAAY,EAAEL,SAAS,CAACM;IAC5B,CAAC,CAAC;EACN,CAAC;EAAA;AAAA;AACD,SAASC,UAAU,CAAC;EAAEC;AAAO,CAAC,EAAE;EAC5B,OAAO,IAAIC,OAAO,CAAC;IACf,cAAc,EAAE,kBAAkB;IAClCC,MAAM,EAAE,kBAAkB;IAC1B,gBAAgB,EAAEF;EACtB,CAAC,CAAC;AACN;AACA,SAASG,kBAAkB,CAACC,SAAS,EAAE;EAAEC;AAAa,CAAC,EAAE;EACrD,MAAMC,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAC;EACrCE,OAAO,CAACC,MAAM,CAAC,eAAe,EAAEC,sBAAsB,CAACH,YAAY,CAAC,CAAC;EACrE,OAAOC,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeG,kBAAkB;EAAA;AAAA;AAAA;EAAA,wCAAjC,WAAkCC,EAAE,EAAE;IAClC,MAAMC,MAAM,SAASD,EAAE,EAAE;IACzB,IAAIC,MAAM,CAACb,MAAM,IAAI,GAAG,IAAIa,MAAM,CAACb,MAAM,GAAG,GAAG,EAAE;MAC7C;MACA,OAAOY,EAAE,EAAE;IACf;IACA,OAAOC,MAAM;EACjB,CAAC;EAAA;AAAA;AACD,SAAS3B,iCAAiC,CAAC4B,iBAAiB,EAAE;EAC1D;EACA,OAAOC,MAAM,CAACD,iBAAiB,CAACE,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACxD;AACA,SAASN,sBAAsB,CAACH,YAAY,EAAE;EAC1C,OAAQ,GAAEvC,qBAAsB,IAAGuC,YAAa,EAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeU,yBAAyB;EAAA;AAAA;AAwCxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;EAAA,+CAxDA,WAAyC;IAAEX,SAAS;IAAEY;EAAyB,CAAC,EAAE;IAAEC;EAAI,CAAC,EAAE;IACvF,MAAMC,QAAQ,GAAGzC,wBAAwB,CAAC2B,SAAS,CAAC;IACpD,MAAME,OAAO,GAAGP,UAAU,CAACK,SAAS,CAAC;IACrC;IACA,MAAMe,gBAAgB,GAAGH,wBAAwB,CAACI,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,EAAE;MACrE,IAAID,gBAAgB,EAAE;QAClBhB,OAAO,CAACC,MAAM,CAAC,mBAAmB,EAAEe,gBAAgB,CAAC;MACzD;IACJ;IACA,MAAME,IAAI,GAAG;MACTP,GAAG;MACHQ,WAAW,EAAE3D,qBAAqB;MAClC4D,KAAK,EAAEtB,SAAS,CAACsB,KAAK;MACtBC,UAAU,EAAE9D;IAChB,CAAC;IACD,MAAM+D,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACdvB,OAAO;MACPkB,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;IAC7B,CAAC;IACD,MAAM5C,QAAQ,SAAS6B,kBAAkB,CAAC,MAAMuB,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAIhD,QAAQ,CAACqD,EAAE,EAAE;MACb,MAAMC,aAAa,SAAStD,QAAQ,CAACW,IAAI,EAAE;MAC3C,MAAM4C,2BAA2B,GAAG;QAChClB,GAAG,EAAEiB,aAAa,CAACjB,GAAG,IAAIA,GAAG;QAC7BmB,kBAAkB,EAAE,CAAC,CAAC;QACtB/B,YAAY,EAAE6B,aAAa,CAAC7B,YAAY;QACxCgC,SAAS,EAAE1D,gCAAgC,CAACuD,aAAa,CAACG,SAAS;MACvE,CAAC;MACD,OAAOF,2BAA2B;IACtC,CAAC,MACI;MACD,YAAY/C,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA;AAAA;AAmBD,SAAS0D,KAAK,CAACC,EAAE,EAAE;EACf,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC1BC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC;EAC3B,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqB,CAACC,KAAK,EAAE;EAClC,MAAMC,GAAG,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAGJ,KAAK,CAAC,CAAC;EAC/C,OAAOC,GAAG,CAAC/B,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmC,iBAAiB,GAAG,mBAAmB;AAC7C,MAAMC,WAAW,GAAG,EAAE;AACtB;AACA;AACA;AACA;AACA,SAASC,WAAW,GAAG;EACnB,IAAI;IACA;IACA;IACA,MAAMC,YAAY,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IACvC,MAAMC,MAAM,GAAGC,IAAI,CAACD,MAAM,IAAIC,IAAI,CAACC,QAAQ;IAC3CF,MAAM,CAACG,eAAe,CAACL,YAAY,CAAC;IACpC;IACAA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,GAAIA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAW;IAC7D,MAAMnC,GAAG,GAAGyC,MAAM,CAACN,YAAY,CAAC;IAChC,OAAOH,iBAAiB,CAACU,IAAI,CAAC1C,GAAG,CAAC,GAAGA,GAAG,GAAGiC,WAAW;EAC1D,CAAC,CACD,OAAOU,EAAE,EAAE;IACP;IACA,OAAOV,WAAW;EACtB;AACJ;AACA;AACA,SAASQ,MAAM,CAACN,YAAY,EAAE;EAC1B,MAAMS,SAAS,GAAGlB,qBAAqB,CAACS,YAAY,CAAC;EACrD;EACA;EACA,OAAOS,SAAS,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAM,CAAC3D,SAAS,EAAE;EACvB,OAAQ,GAAEA,SAAS,CAAC4D,OAAQ,IAAG5D,SAAS,CAACsB,KAAM,EAAC;AACpD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,kBAAkB,GAAG,IAAIC,GAAG,EAAE;AACpC;AACA;AACA;AACA;AACA,SAASC,UAAU,CAAC/D,SAAS,EAAEa,GAAG,EAAE;EAChC,MAAMmD,GAAG,GAAGL,MAAM,CAAC3D,SAAS,CAAC;EAC7BiE,sBAAsB,CAACD,GAAG,EAAEnD,GAAG,CAAC;EAChCqD,kBAAkB,CAACF,GAAG,EAAEnD,GAAG,CAAC;AAChC;AACA,SAASsD,WAAW,CAACnE,SAAS,EAAEoE,QAAQ,EAAE;EACtC;EACA;EACAC,mBAAmB,EAAE;EACrB,MAAML,GAAG,GAAGL,MAAM,CAAC3D,SAAS,CAAC;EAC7B,IAAIsE,WAAW,GAAGT,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC7C,IAAI,CAACM,WAAW,EAAE;IACdA,WAAW,GAAG,IAAIE,GAAG,EAAE;IACvBX,kBAAkB,CAACY,GAAG,CAACT,GAAG,EAAEM,WAAW,CAAC;EAC5C;EACAA,WAAW,CAACI,GAAG,CAACN,QAAQ,CAAC;AAC7B;AACA,SAASO,cAAc,CAAC3E,SAAS,EAAEoE,QAAQ,EAAE;EACzC,MAAMJ,GAAG,GAAGL,MAAM,CAAC3D,SAAS,CAAC;EAC7B,MAAMsE,WAAW,GAAGT,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC/C,IAAI,CAACM,WAAW,EAAE;IACd;EACJ;EACAA,WAAW,CAACM,MAAM,CAACR,QAAQ,CAAC;EAC5B,IAAIE,WAAW,CAACO,IAAI,KAAK,CAAC,EAAE;IACxBhB,kBAAkB,CAACe,MAAM,CAACZ,GAAG,CAAC;EAClC;EACA;EACAc,qBAAqB,EAAE;AAC3B;AACA,SAASb,sBAAsB,CAACD,GAAG,EAAEnD,GAAG,EAAE;EACtC,MAAMkE,SAAS,GAAGlB,kBAAkB,CAACU,GAAG,CAACP,GAAG,CAAC;EAC7C,IAAI,CAACe,SAAS,EAAE;IACZ;EACJ;EACA,KAAK,MAAMX,QAAQ,IAAIW,SAAS,EAAE;IAC9BX,QAAQ,CAACvD,GAAG,CAAC;EACjB;AACJ;AACA,SAASqD,kBAAkB,CAACF,GAAG,EAAEnD,GAAG,EAAE;EAClC,MAAMmE,OAAO,GAAGX,mBAAmB,EAAE;EACrC,IAAIW,OAAO,EAAE;IACTA,OAAO,CAACC,WAAW,CAAC;MAAEjB,GAAG;MAAEnD;IAAI,CAAC,CAAC;EACrC;EACAiE,qBAAqB,EAAE;AAC3B;AACA,IAAII,gBAAgB,GAAG,IAAI;AAC3B;AACA,SAASb,mBAAmB,GAAG;EAC3B,IAAI,CAACa,gBAAgB,IAAI,kBAAkB,IAAI/B,IAAI,EAAE;IACjD+B,gBAAgB,GAAG,IAAIC,gBAAgB,CAAC,uBAAuB,CAAC;IAChED,gBAAgB,CAACE,SAAS,GAAGC,CAAC,IAAI;MAC9BpB,sBAAsB,CAACoB,CAAC,CAACC,IAAI,CAACtB,GAAG,EAAEqB,CAAC,CAACC,IAAI,CAACzE,GAAG,CAAC;IAClD,CAAC;EACL;EACA,OAAOqE,gBAAgB;AAC3B;AACA,SAASJ,qBAAqB,GAAG;EAC7B,IAAIjB,kBAAkB,CAACgB,IAAI,KAAK,CAAC,IAAIK,gBAAgB,EAAE;IACnDA,gBAAgB,CAACK,KAAK,EAAE;IACxBL,gBAAgB,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAG,iCAAiC;AACvD,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,iBAAiB,GAAG,8BAA8B;AACxD,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAY,GAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAGtI,MAAM,CAACmI,aAAa,EAAEC,gBAAgB,EAAE;MAChDI,OAAO,EAAE,CAACC,EAAE,EAAEC,UAAU,KAAK;QACzB;QACA;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACFD,EAAE,CAACE,iBAAiB,CAACN,iBAAiB,CAAC;QAAC;MAEpD;IACJ,CAAC,CAAC;EACN;EACA,OAAOC,SAAS;AACpB;AACA;AAAA,SACelB,GAAG;EAAA;AAAA;AAalB;AAAA;EAAA,yBAbA,WAAmBzE,SAAS,EAAEiG,KAAK,EAAE;IACjC,MAAMjC,GAAG,GAAGL,MAAM,CAAC3D,SAAS,CAAC;IAC7B,MAAM8F,EAAE,SAASF,YAAY,EAAE;IAC/B,MAAMM,EAAE,GAAGJ,EAAE,CAACK,WAAW,CAACT,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMU,WAAW,GAAGF,EAAE,CAACE,WAAW,CAACV,iBAAiB,CAAC;IACrD,MAAMW,QAAQ,SAAUD,WAAW,CAAC7B,GAAG,CAACP,GAAG,CAAE;IAC7C,MAAMoC,WAAW,CAACE,GAAG,CAACL,KAAK,EAAEjC,GAAG,CAAC;IACjC,MAAMkC,EAAE,CAACK,IAAI;IACb,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACxF,GAAG,KAAKoF,KAAK,CAACpF,GAAG,EAAE;MACzCkD,UAAU,CAAC/D,SAAS,EAAEiG,KAAK,CAACpF,GAAG,CAAC;IACpC;IACA,OAAOoF,KAAK;EAChB,CAAC;EAAA;AAAA;AAAA,SAEcO,MAAM;EAAA;AAAA;AAOrB;AACA;AACA;AACA;AACA;AACA;AALA;EAAA,4BAPA,WAAsBxG,SAAS,EAAE;IAC7B,MAAMgE,GAAG,GAAGL,MAAM,CAAC3D,SAAS,CAAC;IAC7B,MAAM8F,EAAE,SAASF,YAAY,EAAE;IAC/B,MAAMM,EAAE,GAAGJ,EAAE,CAACK,WAAW,CAACT,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMQ,EAAE,CAACE,WAAW,CAACV,iBAAiB,CAAC,CAACd,MAAM,CAACZ,GAAG,CAAC;IACnD,MAAMkC,EAAE,CAACK,IAAI;EACjB,CAAC;EAAA;AAAA;AAAA,SAOcE,MAAM;EAAA;AAAA;AAoBrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;EAAA,4BApCA,WAAsBzG,SAAS,EAAE0G,QAAQ,EAAE;IACvC,MAAM1C,GAAG,GAAGL,MAAM,CAAC3D,SAAS,CAAC;IAC7B,MAAM8F,EAAE,SAASF,YAAY,EAAE;IAC/B,MAAMM,EAAE,GAAGJ,EAAE,CAACK,WAAW,CAACT,iBAAiB,EAAE,WAAW,CAAC;IACzD,MAAMiB,KAAK,GAAGT,EAAE,CAACE,WAAW,CAACV,iBAAiB,CAAC;IAC/C,MAAMW,QAAQ,SAAUM,KAAK,CAACpC,GAAG,CAACP,GAAG,CAAE;IACvC,MAAM4C,QAAQ,GAAGF,QAAQ,CAACL,QAAQ,CAAC;IACnC,IAAIO,QAAQ,KAAKC,SAAS,EAAE;MACxB,MAAMF,KAAK,CAAC/B,MAAM,CAACZ,GAAG,CAAC;IAC3B,CAAC,MACI;MACD,MAAM2C,KAAK,CAACL,GAAG,CAACM,QAAQ,EAAE5C,GAAG,CAAC;IAClC;IACA,MAAMkC,EAAE,CAACK,IAAI;IACb,IAAIK,QAAQ,KAAK,CAACP,QAAQ,IAAIA,QAAQ,CAACxF,GAAG,KAAK+F,QAAQ,CAAC/F,GAAG,CAAC,EAAE;MAC1DkD,UAAU,CAAC/D,SAAS,EAAE4G,QAAQ,CAAC/F,GAAG,CAAC;IACvC;IACA,OAAO+F,QAAQ;EACnB,CAAC;EAAA;AAAA;AAAA,SAsBcE,oBAAoB;EAAA;AAAA;AAiBnC;AACA;AACA;AACA;AAHA;EAAA,0CAjBA,WAAoCC,aAAa,EAAE;IAC/C,IAAIC,mBAAmB;IACvB,MAAMC,iBAAiB,SAASR,MAAM,CAACM,aAAa,CAAC/G,SAAS,EAAEkH,QAAQ,IAAI;MACxE,MAAMD,iBAAiB,GAAGE,+BAA+B,CAACD,QAAQ,CAAC;MACnE,MAAME,gBAAgB,GAAGC,8BAA8B,CAACN,aAAa,EAAEE,iBAAiB,CAAC;MACzFD,mBAAmB,GAAGI,gBAAgB,CAACJ,mBAAmB;MAC1D,OAAOI,gBAAgB,CAACH,iBAAiB;IAC7C,CAAC,CAAC;IACF,IAAIA,iBAAiB,CAACpG,GAAG,KAAKiC,WAAW,EAAE;MACvC;MACA,OAAO;QAAEmE,iBAAiB,QAAQD;MAAoB,CAAC;IAC3D;IACA,OAAO;MACHC,iBAAiB;MACjBD;IACJ,CAAC;EACL,CAAC;EAAA;AAAA;AAKD,SAASG,+BAA+B,CAACD,QAAQ,EAAE;EAC/C,MAAMI,KAAK,GAAGJ,QAAQ,IAAI;IACtBrG,GAAG,EAAEkC,WAAW,EAAE;IAClBf,kBAAkB,EAAE,CAAC,CAAC;EAC1B,CAAC;;EACD,OAAOuF,oBAAoB,CAACD,KAAK,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,8BAA8B,CAACN,aAAa,EAAEE,iBAAiB,EAAE;EACtE,IAAIA,iBAAiB,CAACjF,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;IAC5E,IAAI,CAACwF,SAAS,CAACC,MAAM,EAAE;MACnB;MACA,MAAMC,4BAA4B,GAAGtF,OAAO,CAACuF,MAAM,CAAC3J,aAAa,CAACqB,MAAM,CAAC,aAAa,CAAC,4BAA4B,CAAC;MACpH,OAAO;QACH4H,iBAAiB;QACjBD,mBAAmB,EAAEU;MACzB,CAAC;IACL;IACA;IACA,MAAME,eAAe,GAAG;MACpB/G,GAAG,EAAEoG,iBAAiB,CAACpG,GAAG;MAC1BmB,kBAAkB,EAAE,CAAC,CAAC;MACtB6F,gBAAgB,EAAE/I,IAAI,CAACC,GAAG;IAC9B,CAAC;IACD,MAAMiI,mBAAmB,GAAGc,oBAAoB,CAACf,aAAa,EAAEa,eAAe,CAAC;IAChF,OAAO;MAAEX,iBAAiB,EAAEW,eAAe;MAAEZ;IAAoB,CAAC;EACtE,CAAC,MACI,IAAIC,iBAAiB,CAACjF,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;IACjF,OAAO;MACHiF,iBAAiB;MACjBD,mBAAmB,EAAEe,wBAAwB,CAAChB,aAAa;IAC/D,CAAC;EACL,CAAC,MACI;IACD,OAAO;MAAEE;IAAkB,CAAC;EAChC;AACJ;AACA;AAAA,SACea,oBAAoB;EAAA;AAAA;AAqBnC;AAAA;EAAA,0CArBA,WAAoCf,aAAa,EAAEE,iBAAiB,EAAE;IAClE,IAAI;MACA,MAAMlF,2BAA2B,SAASpB,yBAAyB,CAACoG,aAAa,EAAEE,iBAAiB,CAAC;MACrG,OAAOxC,GAAG,CAACsC,aAAa,CAAC/G,SAAS,EAAE+B,2BAA2B,CAAC;IACpE,CAAC,CACD,OAAOsD,CAAC,EAAE;MACN,IAAIpH,aAAa,CAACoH,CAAC,CAAC,IAAIA,CAAC,CAAC2C,UAAU,CAAC1I,UAAU,KAAK,GAAG,EAAE;QACrD;QACA;QACA,MAAMkH,MAAM,CAACO,aAAa,CAAC/G,SAAS,CAAC;MACzC,CAAC,MACI;QACD;QACA,MAAMyE,GAAG,CAACsC,aAAa,CAAC/G,SAAS,EAAE;UAC/Ba,GAAG,EAAEoG,iBAAiB,CAACpG,GAAG;UAC1BmB,kBAAkB,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC;MACN;;MACA,MAAMqD,CAAC;IACX;EACJ,CAAC;EAAA;AAAA;AAAA,SAEc0C,wBAAwB;EAAA;AAAA;AAuBvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;EAAA,8CAvBA,WAAwChB,aAAa,EAAE;IACnD;IACA;IACA;IACA,IAAIO,KAAK,SAASW,yBAAyB,CAAClB,aAAa,CAAC/G,SAAS,CAAC;IACpE,OAAOsH,KAAK,CAACtF,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MACnE;MACA,MAAME,KAAK,CAAC,GAAG,CAAC;MAChBoF,KAAK,SAASW,yBAAyB,CAAClB,aAAa,CAAC/G,SAAS,CAAC;IACpE;IACA,IAAIsH,KAAK,CAACtF,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;MAChE;MACA,MAAM;QAAEiF,iBAAiB;QAAED;MAAoB,CAAC,SAASF,oBAAoB,CAACC,aAAa,CAAC;MAC5F,IAAIC,mBAAmB,EAAE;QACrB,OAAOA,mBAAmB;MAC9B,CAAC,MACI;QACD;QACA,OAAOC,iBAAiB;MAC5B;IACJ;IACA,OAAOK,KAAK;EAChB,CAAC;EAAA;AAAA;AASD,SAASW,yBAAyB,CAACjI,SAAS,EAAE;EAC1C,OAAOyG,MAAM,CAACzG,SAAS,EAAEkH,QAAQ,IAAI;IACjC,IAAI,CAACA,QAAQ,EAAE;MACX,MAAMlJ,aAAa,CAACqB,MAAM,CAAC,wBAAwB,CAAC,uCAAuC;IAC/F;;IACA,OAAOkI,oBAAoB,CAACL,QAAQ,CAAC;EACzC,CAAC,CAAC;AACN;AACA,SAASK,oBAAoB,CAACD,KAAK,EAAE;EACjC,IAAIY,8BAA8B,CAACZ,KAAK,CAAC,EAAE;IACvC,OAAO;MACHzG,GAAG,EAAEyG,KAAK,CAACzG,GAAG;MACdmB,kBAAkB,EAAE,CAAC,CAAC;IAC1B,CAAC;EACL;;EACA,OAAOsF,KAAK;AAChB;AACA,SAASY,8BAA8B,CAACjB,iBAAiB,EAAE;EACvD,OAAQA,iBAAiB,CAACjF,kBAAkB,KAAK,CAAC,CAAC,mCAC/CiF,iBAAiB,CAACY,gBAAgB,GAAGrK,kBAAkB,GAAGsB,IAAI,CAACC,GAAG,EAAE;AAC5E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeoJ,wBAAwB;EAAA;AAAA;AAAA;EAAA,8CAAvC,WAAwC;IAAEnI,SAAS;IAAEY;EAAyB,CAAC,EAAEqG,iBAAiB,EAAE;IAChG,MAAMnG,QAAQ,GAAGsH,4BAA4B,CAACpI,SAAS,EAAEiH,iBAAiB,CAAC;IAC3E,MAAM/G,OAAO,GAAGH,kBAAkB,CAACC,SAAS,EAAEiH,iBAAiB,CAAC;IAChE;IACA,MAAMlG,gBAAgB,GAAGH,wBAAwB,CAACI,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,EAAE;MACrE,IAAID,gBAAgB,EAAE;QAClBhB,OAAO,CAACC,MAAM,CAAC,mBAAmB,EAAEe,gBAAgB,CAAC;MACzD;IACJ;IACA,MAAME,IAAI,GAAG;MACTiH,YAAY,EAAE;QACV9G,UAAU,EAAE9D,eAAe;QAC3B6D,KAAK,EAAEtB,SAAS,CAACsB;MACrB;IACJ,CAAC;IACD,MAAME,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACdvB,OAAO;MACPkB,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;IAC7B,CAAC;IACD,MAAM5C,QAAQ,SAAS6B,kBAAkB,CAAC,MAAMuB,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAIhD,QAAQ,CAACqD,EAAE,EAAE;MACb,MAAMC,aAAa,SAAStD,QAAQ,CAACW,IAAI,EAAE;MAC3C,MAAMmJ,kBAAkB,GAAG/J,gCAAgC,CAACuD,aAAa,CAAC;MAC1E,OAAOwG,kBAAkB;IAC7B,CAAC,MACI;MACD,YAAYtJ,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA;AAAA;AACD,SAAS4J,4BAA4B,CAACpI,SAAS,EAAE;EAAEa;AAAI,CAAC,EAAE;EACtD,OAAQ,GAAExC,wBAAwB,CAAC2B,SAAS,CAAE,IAAGa,GAAI,sBAAqB;AAC9E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMe0H,gBAAgB;EAAA;AAAA;AA+B/B;AACA;AACA;AACA;AACA;AACA;AALA;EAAA,sCA/BA,WAAgCxB,aAAa,EAAEyB,YAAY,GAAG,KAAK,EAAE;IACjE,IAAIC,YAAY;IAChB,MAAMnB,KAAK,SAASb,MAAM,CAACM,aAAa,CAAC/G,SAAS,EAAEkH,QAAQ,IAAI;MAC5D,IAAI,CAACwB,iBAAiB,CAACxB,QAAQ,CAAC,EAAE;QAC9B,MAAMlJ,aAAa,CAACqB,MAAM,CAAC,gBAAgB,CAAC,+BAA+B;MAC/E;;MACA,MAAMsJ,YAAY,GAAGzB,QAAQ,CAACjF,SAAS;MACvC,IAAI,CAACuG,YAAY,IAAII,gBAAgB,CAACD,YAAY,CAAC,EAAE;QACjD;QACA,OAAOzB,QAAQ;MACnB,CAAC,MACI,IAAIyB,YAAY,CAACjK,aAAa,KAAK,CAAC,CAAC,iCAAiC;QACvE;QACA+J,YAAY,GAAGI,yBAAyB,CAAC9B,aAAa,EAAEyB,YAAY,CAAC;QACrE,OAAOtB,QAAQ;MACnB,CAAC,MACI;QACD;QACA,IAAI,CAACM,SAAS,CAACC,MAAM,EAAE;UACnB,MAAMzJ,aAAa,CAACqB,MAAM,CAAC,aAAa,CAAC,4BAA4B;QACzE;;QACA,MAAMuI,eAAe,GAAGkB,mCAAmC,CAAC5B,QAAQ,CAAC;QACrEuB,YAAY,GAAGM,wBAAwB,CAAChC,aAAa,EAAEa,eAAe,CAAC;QACvE,OAAOA,eAAe;MAC1B;IACJ,CAAC,CAAC;IACF,MAAM3F,SAAS,GAAGwG,YAAY,SAClBA,YAAY,GAClBnB,KAAK,CAACrF,SAAS;IACrB,OAAOA,SAAS;EACpB,CAAC;EAAA;AAAA;AAAA,SAOc4G,yBAAyB;EAAA;AAAA;AAmBxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;EAAA,+CAnBA,WAAyC9B,aAAa,EAAEyB,YAAY,EAAE;IAClE;IACA;IACA;IACA,IAAIlB,KAAK,SAAS0B,sBAAsB,CAACjC,aAAa,CAAC/G,SAAS,CAAC;IACjE,OAAOsH,KAAK,CAACrF,SAAS,CAACvD,aAAa,KAAK,CAAC,CAAC,iCAAiC;MACxE;MACA,MAAMwD,KAAK,CAAC,GAAG,CAAC;MAChBoF,KAAK,SAAS0B,sBAAsB,CAACjC,aAAa,CAAC/G,SAAS,CAAC;IACjE;IACA,MAAMiC,SAAS,GAAGqF,KAAK,CAACrF,SAAS;IACjC,IAAIA,SAAS,CAACvD,aAAa,KAAK,CAAC,CAAC,iCAAiC;MAC/D;MACA,OAAO6J,gBAAgB,CAACxB,aAAa,EAAEyB,YAAY,CAAC;IACxD,CAAC,MACI;MACD,OAAOvG,SAAS;IACpB;EACJ,CAAC;EAAA;AAAA;AASD,SAAS+G,sBAAsB,CAAChJ,SAAS,EAAE;EACvC,OAAOyG,MAAM,CAACzG,SAAS,EAAEkH,QAAQ,IAAI;IACjC,IAAI,CAACwB,iBAAiB,CAACxB,QAAQ,CAAC,EAAE;MAC9B,MAAMlJ,aAAa,CAACqB,MAAM,CAAC,gBAAgB,CAAC,+BAA+B;IAC/E;;IACA,MAAMsJ,YAAY,GAAGzB,QAAQ,CAACjF,SAAS;IACvC,IAAIgH,2BAA2B,CAACN,YAAY,CAAC,EAAE;MAC3C,OAAOO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjC,QAAQ,CAAC,EAAE;QAAEjF,SAAS,EAAE;UAAEvD,aAAa,EAAE,CAAC,CAAC;QAAgC;MAAE,CAAC,CAAC;IAC1H;;IACA,OAAOwI,QAAQ;EACnB,CAAC,CAAC;AACN;AAAC,SACc6B,wBAAwB;EAAA;AAAA;AAAA;EAAA,8CAAvC,WAAwChC,aAAa,EAAEE,iBAAiB,EAAE;IACtE,IAAI;MACA,MAAMhF,SAAS,SAASkG,wBAAwB,CAACpB,aAAa,EAAEE,iBAAiB,CAAC;MAClF,MAAMmC,wBAAwB,GAAGF,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElC,iBAAiB,CAAC,EAAE;QAAEhF;MAAU,CAAC,CAAC;MACnG,MAAMwC,GAAG,CAACsC,aAAa,CAAC/G,SAAS,EAAEoJ,wBAAwB,CAAC;MAC5D,OAAOnH,SAAS;IACpB,CAAC,CACD,OAAOoD,CAAC,EAAE;MACN,IAAIpH,aAAa,CAACoH,CAAC,CAAC,KACfA,CAAC,CAAC2C,UAAU,CAAC1I,UAAU,KAAK,GAAG,IAAI+F,CAAC,CAAC2C,UAAU,CAAC1I,UAAU,KAAK,GAAG,CAAC,EAAE;QACtE;QACA;QACA,MAAMkH,MAAM,CAACO,aAAa,CAAC/G,SAAS,CAAC;MACzC,CAAC,MACI;QACD,MAAMoJ,wBAAwB,GAAGF,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElC,iBAAiB,CAAC,EAAE;UAAEhF,SAAS,EAAE;YAAEvD,aAAa,EAAE,CAAC,CAAC;UAAgC;QAAE,CAAC,CAAC;QACzJ,MAAM+F,GAAG,CAACsC,aAAa,CAAC/G,SAAS,EAAEoJ,wBAAwB,CAAC;MAChE;MACA,MAAM/D,CAAC;IACX;EACJ,CAAC;EAAA;AAAA;AACD,SAASqD,iBAAiB,CAACzB,iBAAiB,EAAE;EAC1C,OAAQA,iBAAiB,KAAKJ,SAAS,IACnCI,iBAAiB,CAACjF,kBAAkB,KAAK,CAAC,CAAC;AACnD;;AACA,SAAS4G,gBAAgB,CAAC3G,SAAS,EAAE;EACjC,OAAQA,SAAS,CAACvD,aAAa,KAAK,CAAC,CAAC,iCAClC,CAAC2K,kBAAkB,CAACpH,SAAS,CAAC;AACtC;AACA,SAASoH,kBAAkB,CAACpH,SAAS,EAAE;EACnC,MAAMlD,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE;EACtB,OAAQA,GAAG,GAAGkD,SAAS,CAACpD,YAAY,IAChCoD,SAAS,CAACpD,YAAY,GAAGoD,SAAS,CAACtD,SAAS,GAAGI,GAAG,GAAGnB,uBAAuB;AACpF;AACA;AACA,SAASkL,mCAAmC,CAAC5B,QAAQ,EAAE;EACnD,MAAMoC,mBAAmB,GAAG;IACxB5K,aAAa,EAAE,CAAC,CAAC;IACjB6K,WAAW,EAAEzK,IAAI,CAACC,GAAG;EACzB,CAAC;EACD,OAAOmK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjC,QAAQ,CAAC,EAAE;IAAEjF,SAAS,EAAEqH;EAAoB,CAAC,CAAC;AACzF;AACA,SAASL,2BAA2B,CAAChH,SAAS,EAAE;EAC5C,OAAQA,SAAS,CAACvD,aAAa,KAAK,CAAC,CAAC,mCAClCuD,SAAS,CAACsH,WAAW,GAAG/L,kBAAkB,GAAGsB,IAAI,CAACC,GAAG,EAAE;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOeyK,KAAK;EAAA;AAAA;AAcpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;EAAA,2BA9BA,WAAqBzC,aAAa,EAAE;IAChC,MAAM0C,iBAAiB,GAAG1C,aAAa;IACvC,MAAM;MAAEE,iBAAiB;MAAED;IAAoB,CAAC,SAASF,oBAAoB,CAAC2C,iBAAiB,CAAC;IAChG,IAAIzC,mBAAmB,EAAE;MACrBA,mBAAmB,CAAC0C,KAAK,CAACC,OAAO,CAACzL,KAAK,CAAC;IAC5C,CAAC,MACI;MACD;MACA;MACAqK,gBAAgB,CAACkB,iBAAiB,CAAC,CAACC,KAAK,CAACC,OAAO,CAACzL,KAAK,CAAC;IAC5D;IACA,OAAO+I,iBAAiB,CAACpG,GAAG;EAChC,CAAC;EAAA;AAAA;AAAA,SA0Bc+I,QAAQ;EAAA;AAAA;AAAA;EAAA,8BAAvB,WAAwB7C,aAAa,EAAEyB,YAAY,GAAG,KAAK,EAAE;IACzD,MAAMiB,iBAAiB,GAAG1C,aAAa;IACvC,MAAM8C,gCAAgC,CAACJ,iBAAiB,CAAC;IACzD;IACA;IACA,MAAMxH,SAAS,SAASsG,gBAAgB,CAACkB,iBAAiB,EAAEjB,YAAY,CAAC;IACzE,OAAOvG,SAAS,CAACxD,KAAK;EAC1B,CAAC;EAAA;AAAA;AAAA,SACcoL,gCAAgC;EAAA;AAAA;AAQ/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;EAAA,sDARA,WAAgD9C,aAAa,EAAE;IAC3D,MAAM;MAAEC;IAAoB,CAAC,SAASF,oBAAoB,CAACC,aAAa,CAAC;IACzE,IAAIC,mBAAmB,EAAE;MACrB;MACA,MAAMA,mBAAmB;IAC7B;EACJ,CAAC;EAAA;AAAA;AAAA,SAkBc8C,yBAAyB;EAAA;AAAA;AAAA;EAAA,+CAAxC,WAAyC9J,SAAS,EAAEiH,iBAAiB,EAAE;IACnE,MAAMnG,QAAQ,GAAGiJ,iBAAiB,CAAC/J,SAAS,EAAEiH,iBAAiB,CAAC;IAChE,MAAM/G,OAAO,GAAGH,kBAAkB,CAACC,SAAS,EAAEiH,iBAAiB,CAAC;IAChE,MAAMzF,OAAO,GAAG;MACZC,MAAM,EAAE,QAAQ;MAChBvB;IACJ,CAAC;IACD,MAAM1B,QAAQ,SAAS6B,kBAAkB,CAAC,MAAMuB,KAAK,CAACd,QAAQ,EAAEU,OAAO,CAAC,CAAC;IACzE,IAAI,CAAChD,QAAQ,CAACqD,EAAE,EAAE;MACd,YAAY7C,oBAAoB,CAAC,qBAAqB,EAAER,QAAQ,CAAC;IACrE;EACJ,CAAC;EAAA;AAAA;AACD,SAASuL,iBAAiB,CAAC/J,SAAS,EAAE;EAAEa;AAAI,CAAC,EAAE;EAC3C,OAAQ,GAAExC,wBAAwB,CAAC2B,SAAS,CAAE,IAAGa,GAAI,EAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMemJ,mBAAmB;EAAA;AAAA;AA0BlC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;EAAA,yCA1CA,WAAmCjD,aAAa,EAAE;IAC9C,MAAM;MAAE/G;IAAU,CAAC,GAAG+G,aAAa;IACnC,MAAMO,KAAK,SAASb,MAAM,CAACzG,SAAS,EAAEkH,QAAQ,IAAI;MAC9C,IAAIA,QAAQ,IAAIA,QAAQ,CAAClF,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;QAC/E;QACA,OAAO6E,SAAS;MACpB;MACA,OAAOK,QAAQ;IACnB,CAAC,CAAC;IACF,IAAII,KAAK,EAAE;MACP,IAAIA,KAAK,CAACtF,kBAAkB,KAAK,CAAC,CAAC,iCAAiC;QAChE;QACA,MAAMhE,aAAa,CAACqB,MAAM,CAAC,6BAA6B,CAAC,4CAA4C;MACzG,CAAC,MACI,IAAIiI,KAAK,CAACtF,kBAAkB,KAAK,CAAC,CAAC,+BAA+B;QACnE,IAAI,CAACwF,SAAS,CAACC,MAAM,EAAE;UACnB,MAAMzJ,aAAa,CAACqB,MAAM,CAAC,aAAa,CAAC,4BAA4B;QACzE,CAAC,MACI;UACD,MAAMyK,yBAAyB,CAAC9J,SAAS,EAAEsH,KAAK,CAAC;UACjD,MAAMd,MAAM,CAACxG,SAAS,CAAC;QAC3B;MACJ;IACJ;EACJ,CAAC;EAAA;AAAA;AA2BD,SAASiK,UAAU,CAAClD,aAAa,EAAE3C,QAAQ,EAAE;EACzC,MAAM;IAAEpE;EAAU,CAAC,GAAG+G,aAAa;EACnC5C,WAAW,CAACnE,SAAS,EAAEoE,QAAQ,CAAC;EAChC,OAAO,MAAM;IACTO,cAAc,CAAC3E,SAAS,EAAEoE,QAAQ,CAAC;EACvC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8F,gBAAgB,CAACC,GAAG,GAAGpN,MAAM,EAAE,EAAE;EACtC,MAAM0M,iBAAiB,GAAG3M,YAAY,CAACqN,GAAG,EAAE,eAAe,CAAC,CAACnJ,YAAY,EAAE;EAC3E,OAAOyI,iBAAiB;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,gBAAgB,CAACD,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACE,OAAO,EAAE;IACtB,MAAMC,oBAAoB,CAAC,mBAAmB,CAAC;EACnD;EACA,IAAI,CAACH,GAAG,CAAC7M,IAAI,EAAE;IACX,MAAMgN,oBAAoB,CAAC,UAAU,CAAC;EAC1C;EACA;EACA,MAAMC,UAAU,GAAG,CACf,WAAW,EACX,QAAQ,EACR,OAAO,CACV;EACD,KAAK,MAAMC,OAAO,IAAID,UAAU,EAAE;IAC9B,IAAI,CAACJ,GAAG,CAACE,OAAO,CAACG,OAAO,CAAC,EAAE;MACvB,MAAMF,oBAAoB,CAACE,OAAO,CAAC;IACvC;EACJ;EACA,OAAO;IACH5G,OAAO,EAAEuG,GAAG,CAAC7M,IAAI;IACjBgB,SAAS,EAAE6L,GAAG,CAACE,OAAO,CAAC/L,SAAS;IAChCsB,MAAM,EAAEuK,GAAG,CAACE,OAAO,CAACzK,MAAM;IAC1B0B,KAAK,EAAE6I,GAAG,CAACE,OAAO,CAAC/I;EACvB,CAAC;AACL;AACA,SAASgJ,oBAAoB,CAACG,SAAS,EAAE;EACrC,OAAOzM,aAAa,CAACqB,MAAM,CAAC,2BAA2B,CAAC,2CAA2C;IAC/FoL;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,eAAe;AAC1C,MAAMC,2BAA2B,GAAG,wBAAwB;AAC5D,MAAMC,aAAa,GAAIC,SAAS,IAAK;EACjC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAAC9J,YAAY,EAAE;EACvD;EACA,MAAMhB,SAAS,GAAGoK,gBAAgB,CAACD,GAAG,CAAC;EACvC,MAAMvJ,wBAAwB,GAAG9D,YAAY,CAACqN,GAAG,EAAE,WAAW,CAAC;EAC/D,MAAMV,iBAAiB,GAAG;IACtBU,GAAG;IACHnK,SAAS;IACTY,wBAAwB;IACxBmK,OAAO,EAAE,MAAM3I,OAAO,CAACC,OAAO;EAClC,CAAC;EACD,OAAOoH,iBAAiB;AAC5B,CAAC;AACD,MAAMuB,eAAe,GAAIH,SAAS,IAAK;EACnC,MAAMV,GAAG,GAAGU,SAAS,CAACC,WAAW,CAAC,KAAK,CAAC,CAAC9J,YAAY,EAAE;EACvD;EACA,MAAM+F,aAAa,GAAGjK,YAAY,CAACqN,GAAG,EAAEO,kBAAkB,CAAC,CAAC1J,YAAY,EAAE;EAC1E,MAAMiK,qBAAqB,GAAG;IAC1BzB,KAAK,EAAE,MAAMA,KAAK,CAACzC,aAAa,CAAC;IACjC6C,QAAQ,EAAGpB,YAAY,IAAKoB,QAAQ,CAAC7C,aAAa,EAAEyB,YAAY;EACpE,CAAC;EACD,OAAOyC,qBAAqB;AAChC,CAAC;AACD,SAASC,qBAAqB,GAAG;EAC7BlO,kBAAkB,CAAC,IAAIE,SAAS,CAACwN,kBAAkB,EAAEE,aAAa,EAAE,QAAQ,CAAC,2BAA2B,CAAC;EACzG5N,kBAAkB,CAAC,IAAIE,SAAS,CAACyN,2BAA2B,EAAEK,eAAe,EAAE,SAAS,CAAC,4BAA4B,CAAC;AAC1H;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAE,qBAAqB,EAAE;AACvBjO,eAAe,CAACK,IAAI,EAAEC,OAAO,CAAC;AAC9B;AACAN,eAAe,CAACK,IAAI,EAAEC,OAAO,EAAE,SAAS,CAAC;AAEzC,SAASyM,mBAAmB,EAAER,KAAK,EAAEU,gBAAgB,EAAEN,QAAQ,EAAEK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}