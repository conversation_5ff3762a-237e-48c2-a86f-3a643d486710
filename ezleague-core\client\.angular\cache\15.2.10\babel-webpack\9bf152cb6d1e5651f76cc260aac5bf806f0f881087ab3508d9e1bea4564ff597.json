{"ast": null, "code": "import apply from './_apply.js';\nimport createCtor from './_createCtor.js';\nimport root from './_root.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1;\n\n/**\n * Creates a function that wraps `func` to invoke it with the `this` binding\n * of `thisArg` and `partials` prepended to the arguments it receives.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} partials The arguments to prepend to those provided to\n *  the new function.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createPartial(func, bitmask, thisArg, partials) {\n  var isBind = bitmask & WRAP_BIND_FLAG,\n    Ctor = createCtor(func);\n  function wrapper() {\n    var argsIndex = -1,\n      argsLength = arguments.length,\n      leftIndex = -1,\n      leftLength = partials.length,\n      args = Array(leftLength + argsLength),\n      fn = this && this !== root && this instanceof wrapper ? Ctor : func;\n    while (++leftIndex < leftLength) {\n      args[leftIndex] = partials[leftIndex];\n    }\n    while (argsLength--) {\n      args[leftIndex++] = arguments[++argsIndex];\n    }\n    return apply(fn, isBind ? thisArg : this, args);\n  }\n  return wrapper;\n}\nexport default createPartial;", "map": {"version": 3, "names": ["apply", "createCtor", "root", "WRAP_BIND_FLAG", "createPartial", "func", "bitmask", "thisArg", "partials", "isBind", "Ctor", "wrapper", "argsIndex", "arg<PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "leftIndex", "left<PERSON><PERSON><PERSON>", "args", "Array", "fn"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_createPartial.js"], "sourcesContent": ["import apply from './_apply.js';\nimport createCtor from './_createCtor.js';\nimport root from './_root.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1;\n\n/**\n * Creates a function that wraps `func` to invoke it with the `this` binding\n * of `thisArg` and `partials` prepended to the arguments it receives.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} partials The arguments to prepend to those provided to\n *  the new function.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createPartial(func, bitmask, thisArg, partials) {\n  var isBind = bitmask & WRAP_BIND_FLAG,\n      Ctor = createCtor(func);\n\n  function wrapper() {\n    var argsIndex = -1,\n        argsLength = arguments.length,\n        leftIndex = -1,\n        leftLength = partials.length,\n        args = Array(leftLength + argsLength),\n        fn = (this && this !== root && this instanceof wrapper) ? Ctor : func;\n\n    while (++leftIndex < leftLength) {\n      args[leftIndex] = partials[leftIndex];\n    }\n    while (argsLength--) {\n      args[leftIndex++] = arguments[++argsIndex];\n    }\n    return apply(fn, isBind ? thisArg : this, args);\n  }\n  return wrapper;\n}\n\nexport default createPartial;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,cAAc,GAAG,CAAC;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAa,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACvD,IAAIC,MAAM,GAAGH,OAAO,GAAGH,cAAc;IACjCO,IAAI,GAAGT,UAAU,CAACI,IAAI,CAAC;EAE3B,SAASM,OAAO,GAAG;IACjB,IAAIC,SAAS,GAAG,CAAC,CAAC;MACdC,UAAU,GAAGC,SAAS,CAACC,MAAM;MAC7BC,SAAS,GAAG,CAAC,CAAC;MACdC,UAAU,GAAGT,QAAQ,CAACO,MAAM;MAC5BG,IAAI,GAAGC,KAAK,CAACF,UAAU,GAAGJ,UAAU,CAAC;MACrCO,EAAE,GAAI,IAAI,IAAI,IAAI,KAAKlB,IAAI,IAAI,IAAI,YAAYS,OAAO,GAAID,IAAI,GAAGL,IAAI;IAEzE,OAAO,EAAEW,SAAS,GAAGC,UAAU,EAAE;MAC/BC,IAAI,CAACF,SAAS,CAAC,GAAGR,QAAQ,CAACQ,SAAS,CAAC;IACvC;IACA,OAAOH,UAAU,EAAE,EAAE;MACnBK,IAAI,CAACF,SAAS,EAAE,CAAC,GAAGF,SAAS,CAAC,EAAEF,SAAS,CAAC;IAC5C;IACA,OAAOZ,KAAK,CAACoB,EAAE,EAAEX,MAAM,GAAGF,OAAO,GAAG,IAAI,EAAEW,IAAI,CAAC;EACjD;EACA,OAAOP,OAAO;AAChB;AAEA,eAAeP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}