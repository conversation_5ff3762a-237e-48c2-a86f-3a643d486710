{"ast": null, "code": "/**\n * This method returns `true`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `true`.\n * @example\n *\n * _.times(2, _.stubTrue);\n * // => [true, true]\n */\nfunction stubTrue() {\n  return true;\n}\nexport default stubTrue;", "map": {"version": 3, "names": ["stubTrue"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/stubTrue.js"], "sourcesContent": ["/**\n * This method returns `true`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `true`.\n * @example\n *\n * _.times(2, _.stubTrue);\n * // => [true, true]\n */\nfunction stubTrue() {\n  return true;\n}\n\nexport default stubTrue;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,GAAG;EAClB,OAAO,IAAI;AACb;AAEA,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}