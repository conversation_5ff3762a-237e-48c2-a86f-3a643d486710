{"ast": null, "code": "import Set from './_Set.js';\nimport noop from './noop.js';\nimport setToArray from './_setToArray.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Creates a set object of `values`.\n *\n * @private\n * @param {Array} values The values to add to the set.\n * @returns {Object} Returns the new set.\n */\nvar createSet = !(Set && 1 / setToArray(new Set([, -0]))[1] == INFINITY) ? noop : function (values) {\n  return new Set(values);\n};\nexport default createSet;", "map": {"version": 3, "names": ["Set", "noop", "setToArray", "INFINITY", "createSet", "values"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_createSet.js"], "sourcesContent": ["import Set from './_Set.js';\nimport noop from './noop.js';\nimport setToArray from './_setToArray.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Creates a set object of `values`.\n *\n * @private\n * @param {Array} values The values to add to the set.\n * @returns {Object} Returns the new set.\n */\nvar createSet = !(Set && (1 / setToArray(new Set([,-0]))[1]) == INFINITY) ? noop : function(values) {\n  return new Set(values);\n};\n\nexport default createSet;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,WAAW;AAC3B,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,EAAEJ,GAAG,IAAK,CAAC,GAAGE,UAAU,CAAC,IAAIF,GAAG,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAKG,QAAQ,CAAC,GAAGF,IAAI,GAAG,UAASI,MAAM,EAAE;EAClG,OAAO,IAAIL,GAAG,CAACK,MAAM,CAAC;AACxB,CAAC;AAED,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}