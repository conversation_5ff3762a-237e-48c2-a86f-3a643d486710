<?php

namespace App\Services;

use App\Models\Tournament;
use App\Models\User;
use App\Models\UserClub;
use App\Models\TeamCoach;
use App\Models\StageTeam;
use App\PushNotifications\ScheduleCompleted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TournamentNotificationService
{
    /**
     * Get all users who should be notified about tournament setup completion
     * 
     * @param int $tournamentId
     * @return \Illuminate\Support\Collection
     */
    public function getUsersToNotify($tournamentId)
    {
        try {
            $users = collect();

            // Get all admin users (they should be notified about all tournament activities)
            $adminUsers = User::where('role_id', config('constants.role_base.admin'))
                ->whereNotNull('firebase_token')
                ->where('firebase_token', '!=', 'null')
                ->get();

            $users = $users->merge($adminUsers);

            // Get club managers associated with teams in this tournament
            $clubManagerUsers = $this->getClubManagersForTournament($tournamentId);
            $users = $users->merge($clubManagerUsers);

            // Get team coaches associated with teams in this tournament
            $teamCoachUsers = $this->getTeamCoachesForTournament($tournamentId);
            $users = $users->merge($teamCoachUsers);

            // Remove duplicates based on user ID
            $users = $users->unique('id');

            Log::info("TournamentNotificationService: Found users to notify", [
                'tournament_id' => $tournamentId,
                'total_users' => $users->count(),
                'admin_users' => $adminUsers->count(),
                'club_managers' => $clubManagerUsers->count(),
                'team_coaches' => $teamCoachUsers->count()
            ]);

            return $users;

        } catch (\Exception $e) {
            Log::error("TournamentNotificationService: Error getting users to notify", [
                'tournament_id' => $tournamentId,
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Get club managers for teams participating in the tournament
     * 
     * @param int $tournamentId
     * @return \Illuminate\Support\Collection
     */
    private function getClubManagersForTournament($tournamentId)
    {
        try {
            return User::whereIn('id', function ($query) use ($tournamentId) {
                $query->select('user_clubs.user_id')
                    ->from('user_clubs')
                    ->join('teams', 'user_clubs.club_id', '=', 'teams.club_id')
                    ->join('stage_teams', 'teams.id', '=', 'stage_teams.team_id')
                    ->join('stages', 'stage_teams.stage_id', '=', 'stages.id')
                    ->where('stages.tournament_id', $tournamentId);
            })
                ->whereNotNull('firebase_token')
                ->where('firebase_token', '!=', 'null')
                ->get();

        } catch (\Exception $e) {
            Log::error("TournamentNotificationService: Error getting club managers", [
                'tournament_id' => $tournamentId,
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Get team coaches for teams participating in the tournament
     * 
     * @param int $tournamentId
     * @return \Illuminate\Support\Collection
     */
    private function getTeamCoachesForTournament($tournamentId)
    {
        try {
            return User::whereIn('id', function ($query) use ($tournamentId) {
                $query->select('team_coaches.user_id')
                    ->from('team_coaches')
                    ->join('teams', 'team_coaches.team_id', '=', 'teams.id')
                    ->join('stage_teams', 'teams.id', '=', 'stage_teams.team_id')
                    ->join('stages', 'stage_teams.stage_id', '=', 'stages.id')
                    ->where('stages.tournament_id', $tournamentId);
            })
                ->whereNotNull('firebase_token')
                ->where('firebase_token', '!=', 'null')
                ->get();

        } catch (\Exception $e) {
            Log::error("TournamentNotificationService: Error getting team coaches", [
                'tournament_id' => $tournamentId,
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Send schedule completion notifications to all relevant users
     * 
     * @param int $tournamentId
     * @param int $totalMatches
     * @param int $scheduledMatches
     * @param string|null $jobId
     * @param float|null $executionTime
     * @param string|null $initiatedByUser
     * @return array
     */
    public function sendScheduleCompletedNotifications($tournamentId, $totalMatches, $scheduledMatches, $jobId = null, $executionTime = null, $initiatedByUser = null)
    {
        try {
            $tournament = Tournament::find($tournamentId);
            if (!$tournament) {
                Log::error("TournamentNotificationService: Tournament not found", ['tournament_id' => $tournamentId]);
                return ['success' => false, 'error' => 'Tournament not found'];
            }

            $users = $this->getUsersToNotify($tournamentId);
            $notificationResults = [
                'total_users' => $users->count(),
                'successful_notifications' => 0,
                'failed_notifications' => 0,
                'errors' => []
            ];

            foreach ($users as $user) {
                try {
                    $user->sendNotiScheduleCompleted(
                        $tournament,
                        $totalMatches,
                        $scheduledMatches,
                        $jobId,
                        $executionTime,
                        $initiatedByUser
                    );

                    $notificationResults['successful_notifications']++;

                    Log::info("TournamentNotificationService: Notification sent successfully", [
                        'tournament_id' => $tournamentId,
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'user_role_id' => $user->role_id,
                        'job_id' => $jobId
                    ]);

                } catch (\Exception $e) {
                    $notificationResults['failed_notifications']++;
                    $notificationResults['errors'][] = [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'error' => $e->getMessage()
                    ];

                    Log::error("TournamentNotificationService: Failed to send notification", [
                        'tournament_id' => $tournamentId,
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'user_role_id' => $user->role_id,
                        'error' => $e->getMessage(),
                        'job_id' => $jobId
                    ]);
                }
            }

            Log::info("TournamentNotificationService: Notification batch completed", [
                'tournament_id' => $tournamentId,
                'job_id' => $jobId,
                'results' => $notificationResults
            ]);

            return array_merge(['success' => true], $notificationResults);

        } catch (\Exception $e) {
            Log::error("TournamentNotificationService: Error sending notifications", [
                'tournament_id' => $tournamentId,
                'error' => $e->getMessage(),
                'job_id' => $jobId
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'total_users' => 0,
                'successful_notifications' => 0,
                'failed_notifications' => 0
            ];
        }
    }
}
