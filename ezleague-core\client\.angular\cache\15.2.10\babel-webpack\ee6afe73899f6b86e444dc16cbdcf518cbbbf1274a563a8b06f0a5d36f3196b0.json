{"ast": null, "code": "import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\nimport castPath from './_castPath.js';\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n    length = paths.length,\n    result = {};\n  while (++index < length) {\n    var path = paths[index],\n      value = baseGet(object, path);\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\nexport default basePickBy;", "map": {"version": 3, "names": ["baseGet", "baseSet", "<PERSON><PERSON><PERSON>", "basePickBy", "object", "paths", "predicate", "index", "length", "result", "path", "value"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_basePickBy.js"], "sourcesContent": ["import baseGet from './_baseGet.js';\nimport baseSet from './_baseSet.js';\nimport castPath from './_castPath.js';\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, paths, predicate) {\n  var index = -1,\n      length = paths.length,\n      result = {};\n\n  while (++index < length) {\n    var path = paths[index],\n        value = baseGet(object, path);\n\n    if (predicate(value, path)) {\n      baseSet(result, castPath(path, object), value);\n    }\n  }\n  return result;\n}\n\nexport default basePickBy;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAU,CAACC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC5C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,MAAM,GAAG,CAAC,CAAC;EAEf,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,IAAI,GAAGL,KAAK,CAACE,KAAK,CAAC;MACnBI,KAAK,GAAGX,OAAO,CAACI,MAAM,EAAEM,IAAI,CAAC;IAEjC,IAAIJ,SAAS,CAACK,KAAK,EAAED,IAAI,CAAC,EAAE;MAC1BT,OAAO,CAACQ,MAAM,EAAEP,QAAQ,CAACQ,IAAI,EAAEN,MAAM,CAAC,EAAEO,KAAK,CAAC;IAChD;EACF;EACA,OAAOF,MAAM;AACf;AAEA,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}