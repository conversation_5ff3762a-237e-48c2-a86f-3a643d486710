{"ast": null, "code": "import composeArgs from './_composeArgs.js';\nimport composeArgsRight from './_composeArgsRight.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used as the internal argument placeholder. */\nvar PLACEHOLDER = '__lodash_placeholder__';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_CURRY_BOUND_FLAG = 4,\n  WRAP_CURRY_FLAG = 8,\n  WRAP_ARY_FLAG = 128,\n  WRAP_REARG_FLAG = 256;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Merges the function metadata of `source` into `data`.\n *\n * Merging metadata reduces the number of wrappers used to invoke a function.\n * This is possible because methods like `_.bind`, `_.curry`, and `_.partial`\n * may be applied regardless of execution order. Methods like `_.ary` and\n * `_.rearg` modify function arguments, making the order in which they are\n * executed important, preventing the merging of metadata. However, we make\n * an exception for a safe combined case where curried functions have `_.ary`\n * and or `_.rearg` applied.\n *\n * @private\n * @param {Array} data The destination metadata.\n * @param {Array} source The source metadata.\n * @returns {Array} Returns `data`.\n */\nfunction mergeData(data, source) {\n  var bitmask = data[1],\n    srcBitmask = source[1],\n    newBitmask = bitmask | srcBitmask,\n    isCommon = newBitmask < (WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG | WRAP_ARY_FLAG);\n  var isCombo = srcBitmask == WRAP_ARY_FLAG && bitmask == WRAP_CURRY_FLAG || srcBitmask == WRAP_ARY_FLAG && bitmask == WRAP_REARG_FLAG && data[7].length <= source[8] || srcBitmask == (WRAP_ARY_FLAG | WRAP_REARG_FLAG) && source[7].length <= source[8] && bitmask == WRAP_CURRY_FLAG;\n\n  // Exit early if metadata can't be merged.\n  if (!(isCommon || isCombo)) {\n    return data;\n  }\n  // Use source `thisArg` if available.\n  if (srcBitmask & WRAP_BIND_FLAG) {\n    data[2] = source[2];\n    // Set when currying a bound function.\n    newBitmask |= bitmask & WRAP_BIND_FLAG ? 0 : WRAP_CURRY_BOUND_FLAG;\n  }\n  // Compose partial arguments.\n  var value = source[3];\n  if (value) {\n    var partials = data[3];\n    data[3] = partials ? composeArgs(partials, value, source[4]) : value;\n    data[4] = partials ? replaceHolders(data[3], PLACEHOLDER) : source[4];\n  }\n  // Compose partial right arguments.\n  value = source[5];\n  if (value) {\n    partials = data[5];\n    data[5] = partials ? composeArgsRight(partials, value, source[6]) : value;\n    data[6] = partials ? replaceHolders(data[5], PLACEHOLDER) : source[6];\n  }\n  // Use source `argPos` if available.\n  value = source[7];\n  if (value) {\n    data[7] = value;\n  }\n  // Use source `ary` if it's smaller.\n  if (srcBitmask & WRAP_ARY_FLAG) {\n    data[8] = data[8] == null ? source[8] : nativeMin(data[8], source[8]);\n  }\n  // Use source `arity` if one is not provided.\n  if (data[9] == null) {\n    data[9] = source[9];\n  }\n  // Use source `func` and merge bitmasks.\n  data[0] = source[0];\n  data[1] = newBitmask;\n  return data;\n}\nexport default mergeData;", "map": {"version": 3, "names": ["compose<PERSON><PERSON>s", "composeArgsRight", "replaceHolders", "PLACEHOLDER", "WRAP_BIND_FLAG", "WRAP_BIND_KEY_FLAG", "WRAP_CURRY_BOUND_FLAG", "WRAP_CURRY_FLAG", "WRAP_ARY_FLAG", "WRAP_REARG_FLAG", "nativeMin", "Math", "min", "mergeData", "data", "source", "bitmask", "srcBitmask", "newBitmask", "isCommon", "isCombo", "length", "value", "partials"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_mergeData.js"], "sourcesContent": ["import composeArgs from './_composeArgs.js';\nimport composeArgsRight from './_composeArgsRight.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used as the internal argument placeholder. */\nvar PLACEHOLDER = '__lodash_placeholder__';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_CURRY_BOUND_FLAG = 4,\n    WRAP_CURRY_FLAG = 8,\n    WRAP_ARY_FLAG = 128,\n    WRAP_REARG_FLAG = 256;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Merges the function metadata of `source` into `data`.\n *\n * Merging metadata reduces the number of wrappers used to invoke a function.\n * This is possible because methods like `_.bind`, `_.curry`, and `_.partial`\n * may be applied regardless of execution order. Methods like `_.ary` and\n * `_.rearg` modify function arguments, making the order in which they are\n * executed important, preventing the merging of metadata. However, we make\n * an exception for a safe combined case where curried functions have `_.ary`\n * and or `_.rearg` applied.\n *\n * @private\n * @param {Array} data The destination metadata.\n * @param {Array} source The source metadata.\n * @returns {Array} Returns `data`.\n */\nfunction mergeData(data, source) {\n  var bitmask = data[1],\n      srcBitmask = source[1],\n      newBitmask = bitmask | srcBitmask,\n      isCommon = newBitmask < (WRAP_BIND_FLAG | WRAP_BIND_KEY_FLAG | WRAP_ARY_FLAG);\n\n  var isCombo =\n    ((srcBitmask == WRAP_ARY_FLAG) && (bitmask == WRAP_CURRY_FLAG)) ||\n    ((srcBitmask == WRAP_ARY_FLAG) && (bitmask == WRAP_REARG_FLAG) && (data[7].length <= source[8])) ||\n    ((srcBitmask == (WRAP_ARY_FLAG | WRAP_REARG_FLAG)) && (source[7].length <= source[8]) && (bitmask == WRAP_CURRY_FLAG));\n\n  // Exit early if metadata can't be merged.\n  if (!(isCommon || isCombo)) {\n    return data;\n  }\n  // Use source `thisArg` if available.\n  if (srcBitmask & WRAP_BIND_FLAG) {\n    data[2] = source[2];\n    // Set when currying a bound function.\n    newBitmask |= bitmask & WRAP_BIND_FLAG ? 0 : WRAP_CURRY_BOUND_FLAG;\n  }\n  // Compose partial arguments.\n  var value = source[3];\n  if (value) {\n    var partials = data[3];\n    data[3] = partials ? composeArgs(partials, value, source[4]) : value;\n    data[4] = partials ? replaceHolders(data[3], PLACEHOLDER) : source[4];\n  }\n  // Compose partial right arguments.\n  value = source[5];\n  if (value) {\n    partials = data[5];\n    data[5] = partials ? composeArgsRight(partials, value, source[6]) : value;\n    data[6] = partials ? replaceHolders(data[5], PLACEHOLDER) : source[6];\n  }\n  // Use source `argPos` if available.\n  value = source[7];\n  if (value) {\n    data[7] = value;\n  }\n  // Use source `ary` if it's smaller.\n  if (srcBitmask & WRAP_ARY_FLAG) {\n    data[8] = data[8] == null ? source[8] : nativeMin(data[8], source[8]);\n  }\n  // Use source `arity` if one is not provided.\n  if (data[9] == null) {\n    data[9] = source[9];\n  }\n  // Use source `func` and merge bitmasks.\n  data[0] = source[0];\n  data[1] = newBitmask;\n\n  return data;\n}\n\nexport default mergeData;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,WAAW,GAAG,wBAAwB;;AAE1C;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,kBAAkB,GAAG,CAAC;EACtBC,qBAAqB,GAAG,CAAC;EACzBC,eAAe,GAAG,CAAC;EACnBC,aAAa,GAAG,GAAG;EACnBC,eAAe,GAAG,GAAG;;AAEzB;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAAS,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC/B,IAAIC,OAAO,GAAGF,IAAI,CAAC,CAAC,CAAC;IACjBG,UAAU,GAAGF,MAAM,CAAC,CAAC,CAAC;IACtBG,UAAU,GAAGF,OAAO,GAAGC,UAAU;IACjCE,QAAQ,GAAGD,UAAU,IAAId,cAAc,GAAGC,kBAAkB,GAAGG,aAAa,CAAC;EAEjF,IAAIY,OAAO,GACPH,UAAU,IAAIT,aAAa,IAAMQ,OAAO,IAAIT,eAAgB,IAC5DU,UAAU,IAAIT,aAAa,IAAMQ,OAAO,IAAIP,eAAgB,IAAKK,IAAI,CAAC,CAAC,CAAC,CAACO,MAAM,IAAIN,MAAM,CAAC,CAAC,CAAG,IAC9FE,UAAU,KAAKT,aAAa,GAAGC,eAAe,CAAC,IAAMM,MAAM,CAAC,CAAC,CAAC,CAACM,MAAM,IAAIN,MAAM,CAAC,CAAC,CAAE,IAAKC,OAAO,IAAIT,eAAiB;;EAExH;EACA,IAAI,EAAEY,QAAQ,IAAIC,OAAO,CAAC,EAAE;IAC1B,OAAON,IAAI;EACb;EACA;EACA,IAAIG,UAAU,GAAGb,cAAc,EAAE;IAC/BU,IAAI,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;IACnB;IACAG,UAAU,IAAIF,OAAO,GAAGZ,cAAc,GAAG,CAAC,GAAGE,qBAAqB;EACpE;EACA;EACA,IAAIgB,KAAK,GAAGP,MAAM,CAAC,CAAC,CAAC;EACrB,IAAIO,KAAK,EAAE;IACT,IAAIC,QAAQ,GAAGT,IAAI,CAAC,CAAC,CAAC;IACtBA,IAAI,CAAC,CAAC,CAAC,GAAGS,QAAQ,GAAGvB,WAAW,CAACuB,QAAQ,EAAED,KAAK,EAAEP,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGO,KAAK;IACpER,IAAI,CAAC,CAAC,CAAC,GAAGS,QAAQ,GAAGrB,cAAc,CAACY,IAAI,CAAC,CAAC,CAAC,EAAEX,WAAW,CAAC,GAAGY,MAAM,CAAC,CAAC,CAAC;EACvE;EACA;EACAO,KAAK,GAAGP,MAAM,CAAC,CAAC,CAAC;EACjB,IAAIO,KAAK,EAAE;IACTC,QAAQ,GAAGT,IAAI,CAAC,CAAC,CAAC;IAClBA,IAAI,CAAC,CAAC,CAAC,GAAGS,QAAQ,GAAGtB,gBAAgB,CAACsB,QAAQ,EAAED,KAAK,EAAEP,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGO,KAAK;IACzER,IAAI,CAAC,CAAC,CAAC,GAAGS,QAAQ,GAAGrB,cAAc,CAACY,IAAI,CAAC,CAAC,CAAC,EAAEX,WAAW,CAAC,GAAGY,MAAM,CAAC,CAAC,CAAC;EACvE;EACA;EACAO,KAAK,GAAGP,MAAM,CAAC,CAAC,CAAC;EACjB,IAAIO,KAAK,EAAE;IACTR,IAAI,CAAC,CAAC,CAAC,GAAGQ,KAAK;EACjB;EACA;EACA,IAAIL,UAAU,GAAGT,aAAa,EAAE;IAC9BM,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC,GAAGL,SAAS,CAACI,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;EACvE;EACA;EACA,IAAID,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACnBA,IAAI,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;EACrB;EACA;EACAD,IAAI,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;EACnBD,IAAI,CAAC,CAAC,CAAC,GAAGI,UAAU;EAEpB,OAAOJ,IAAI;AACb;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}