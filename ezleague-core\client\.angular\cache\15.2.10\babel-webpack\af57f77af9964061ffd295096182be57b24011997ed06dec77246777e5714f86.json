{"ast": null, "code": "import baseSet from './_baseSet.js';\n\n/**\n * This method is like `_.set` except that it accepts `customizer` which is\n * invoked to produce the objects of `path`.  If `customizer` returns `undefined`\n * path creation is handled by the method instead. The `customizer` is invoked\n * with three arguments: (nsValue, key, nsObject).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {};\n *\n * _.setWith(object, '[0][1]', 'a', Object);\n * // => { '0': { '1': 'a' } }\n */\nfunction setWith(object, path, value, customizer) {\n  customizer = typeof customizer == 'function' ? customizer : undefined;\n  return object == null ? object : baseSet(object, path, value, customizer);\n}\nexport default setWith;", "map": {"version": 3, "names": ["baseSet", "setWith", "object", "path", "value", "customizer", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/setWith.js"], "sourcesContent": ["import baseSet from './_baseSet.js';\n\n/**\n * This method is like `_.set` except that it accepts `customizer` which is\n * invoked to produce the objects of `path`.  If `customizer` returns `undefined`\n * path creation is handled by the method instead. The `customizer` is invoked\n * with three arguments: (nsValue, key, nsObject).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {};\n *\n * _.setWith(object, '[0][1]', 'a', Object);\n * // => { '0': { '1': 'a' } }\n */\nfunction setWith(object, path, value, customizer) {\n  customizer = typeof customizer == 'function' ? customizer : undefined;\n  return object == null ? object : baseSet(object, path, value, customizer);\n}\n\nexport default setWith;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAO,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;EAChDA,UAAU,GAAG,OAAOA,UAAU,IAAI,UAAU,GAAGA,UAAU,GAAGC,SAAS;EACrE,OAAOJ,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGF,OAAO,CAACE,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,CAAC;AAC3E;AAEA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}