<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * These indexes are specifically designed to optimize the scheduleMatches method
     * and related operations in the ScheduleMatchController.
     */
    public function up(): void
    {
        // Optimize stage_matches queries with cancel_match_types filtering
        Schema::table('stage_matches', function (Blueprint $table) {
            // Composite index for stage_id + status filtering (most common query pattern)
            $table->index(['stage_id', 'status'], 'idx_stage_matches_stage_status');
            
            // Index for ordering matches consistently
            $table->index(['stage_id', 'order', 'id'], 'idx_stage_matches_stage_order');
            
            // Index for home/away team lookups in joins
            $table->index(['home_team_id', 'stage_id'], 'idx_stage_matches_home_team');
            $table->index(['away_team_id', 'stage_id'], 'idx_stage_matches_away_team');
        });

        // Optimize stage_teams queries for group filtering
        Schema::table('stage_teams', function (Blueprint $table) {
            // Composite index for team + stage + group filtering
            $table->index(['team_id', 'stage_id', 'group'], 'idx_stage_teams_team_stage_group');
            
            // Index for group-based filtering
            $table->index(['stage_id', 'group'], 'idx_stage_teams_stage_group');
        });

        // Optimize schedule_matches queries
        Schema::table('schedule_matches', function (Blueprint $table) {
            // Composite index for tournament + stage + match lookups
            $table->index(['tournament_id', 'stage_id', 'match_id'], 'idx_schedule_matches_tournament_stage_match');
            
            // Index for time slot relationships
            $table->index(['time_slot_id'], 'idx_schedule_matches_time_slot');
            
            // Index for referee assignment queries
            $table->index(['tournament_id', 'stage_id'], 'idx_schedule_matches_tournament_stage');
        });

        // Optimize schedule_time_slots queries
        Schema::table('schedule_time_slots', function (Blueprint $table) {
            // Composite index for tournament + stage + location queries
            $table->index(['tournament_id', 'stage_id', 'location_id'], 'idx_time_slots_tournament_stage_location');
            
            // Index for time-based queries and ordering
            $table->index(['tournament_id', 'start_time'], 'idx_time_slots_tournament_start_time');
            $table->index(['tournament_id', 'end_time'], 'idx_time_slots_tournament_end_time');
            
            // Index for location-based time slot management
            $table->index(['location_id', 'start_time'], 'idx_time_slots_location_start_time');
        });

        // Optimize stages queries
        Schema::table('stages', function (Blueprint $table) {
            // Index for tournament + type filtering
            $table->index(['tournament_id', 'type'], 'idx_stages_tournament_type');
        });

        // Optimize season_referees for referee assignment
        Schema::table('season_referees', function (Blueprint $table) {
            // Index for referee availability queries
            $table->index(['id', 'referee_type'], 'idx_season_referees_id_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stage_matches', function (Blueprint $table) {
            $table->dropIndex('idx_stage_matches_stage_status');
            $table->dropIndex('idx_stage_matches_stage_order');
            $table->dropIndex('idx_stage_matches_home_team');
            $table->dropIndex('idx_stage_matches_away_team');
        });

        Schema::table('stage_teams', function (Blueprint $table) {
            $table->dropIndex('idx_stage_teams_team_stage_group');
            $table->dropIndex('idx_stage_teams_stage_group');
        });

        Schema::table('schedule_matches', function (Blueprint $table) {
            $table->dropIndex('idx_schedule_matches_tournament_stage_match');
            $table->dropIndex('idx_schedule_matches_time_slot');
            $table->dropIndex('idx_schedule_matches_tournament_stage');
        });

        Schema::table('schedule_time_slots', function (Blueprint $table) {
            $table->dropIndex('idx_time_slots_tournament_stage_location');
            $table->dropIndex('idx_time_slots_tournament_start_time');
            $table->dropIndex('idx_time_slots_tournament_end_time');
            $table->dropIndex('idx_time_slots_location_start_time');
        });

        Schema::table('stages', function (Blueprint $table) {
            $table->dropIndex('idx_stages_tournament_type');
        });

        Schema::table('season_referees', function (Blueprint $table) {
            $table->dropIndex('idx_season_referees_id_type');
        });
    }
};
