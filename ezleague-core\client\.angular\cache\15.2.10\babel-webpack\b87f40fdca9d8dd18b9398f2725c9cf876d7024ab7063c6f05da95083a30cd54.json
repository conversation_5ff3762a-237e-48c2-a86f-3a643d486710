{"ast": null, "code": "/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n    result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = value;\n  });\n  return result;\n}\nexport default setToArray;", "map": {"version": 3, "names": ["setToArray", "set", "index", "result", "Array", "size", "for<PERSON>ach", "value"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_setToArray.js"], "sourcesContent": ["/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nexport default setToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,CAACC,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGC,KAAK,CAACH,GAAG,CAACI,IAAI,CAAC;EAE5BJ,GAAG,CAACK,OAAO,CAAC,UAASC,KAAK,EAAE;IAC1BJ,MAAM,CAAC,EAAED,KAAK,CAAC,GAAGK,KAAK;EACzB,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}