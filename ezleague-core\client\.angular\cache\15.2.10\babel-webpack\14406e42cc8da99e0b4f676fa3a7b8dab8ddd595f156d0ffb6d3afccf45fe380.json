{"ast": null, "code": "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n  var context = canvas.getContext('2d');\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n  stack.next = stackStart;\n  var stackIn = null,\n    stackOut = null,\n    yw = 0,\n    yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n      pg = pixels[yi + 1],\n      pb = pixels[yi + 2],\n      pa = pixels[yi + 3];\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n    var rInSum = 0,\n      gInSum = 0,\n      bInSum = 0,\n      aInSum = 0,\n      rOutSum = radiusPlus1 * pr,\n      gOutSum = radiusPlus1 * pg,\n      bOutSum = radiusPlus1 * pb,\n      aOutSum = radiusPlus1 * pa,\n      rSum = sumFactor * pr,\n      gSum = sumFactor * pg,\n      bSum = sumFactor * pb,\n      aSum = sumFactor * pa;\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n        g = pixels[p + 1],\n        b = pixels[p + 2],\n        a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n      var _p = x + radius + 1;\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n        _r = _stackOut.r,\n        _g = _stackOut.g,\n        _b = _stackOut.b,\n        _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n    yw += width;\n  }\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n    var _pr = pixels[yi],\n      _pg = pixels[yi + 1],\n      _pb = pixels[yi + 2],\n      _pa = pixels[yi + 3],\n      _rOutSum = radiusPlus1 * _pr,\n      _gOutSum = radiusPlus1 * _pg,\n      _bOutSum = radiusPlus1 * _pb,\n      _aOutSum = radiusPlus1 * _pa,\n      _rSum = sumFactor * _pr,\n      _gSum = sumFactor * _pg,\n      _bSum = sumFactor * _pb,\n      _aSum = sumFactor * _pa;\n    stack = stackStart;\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n    var yp = width;\n    var _gInSum = 0,\n      _bInSum = 0,\n      _aInSum = 0,\n      _rInSum = 0;\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n      var _rbs = radiusPlus1 - _i4;\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n    yi = 0;\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n      pg = pixels[yi + 1],\n      pb = pixels[yi + 2],\n      rOutSum = radiusPlus1 * pr,\n      gOutSum = radiusPlus1 * pg,\n      bOutSum = radiusPlus1 * pb,\n      rSum = sumFactor * pr,\n      gSum = sumFactor * pg,\n      bSum = sumFactor * pb;\n    stack = stackStart;\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n    var rInSum = 0,\n      gInSum = 0,\n      bInSum = 0;\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n    yw += width;\n  }\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n    var _pr2 = pixels[yi],\n      _pg2 = pixels[yi + 1],\n      _pb2 = pixels[yi + 2],\n      _rOutSum2 = radiusPlus1 * _pr2,\n      _gOutSum2 = radiusPlus1 * _pg2,\n      _bOutSum2 = radiusPlus1 * _pb2,\n      _rSum2 = sumFactor * _pr2,\n      _gSum2 = sumFactor * _pg2,\n      _bSum2 = sumFactor * _pb2;\n    stack = stackStart;\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n    var _rInSum2 = 0,\n      _gInSum2 = 0,\n      _bInSum2 = 0;\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n  return imageData;\n}\n/**\n *\n */\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\nexport { BlurStack, processCanvasRGB as canvasRGB, processCanvasRGBA as canvasRGBA, processImage as image, processImageDataRGB as imageDataRGB, processImageDataRGBA as imageDataRGBA };", "map": {"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "mulTable", "shgTable", "processImage", "img", "canvas", "radius", "blurAlphaChannel", "useOffset", "skip<PERSON><PERSON><PERSON>", "document", "getElementById", "Object", "toString", "call", "slice", "dimensionType", "w", "h", "width", "height", "style", "context", "getContext", "clearRect", "drawImage", "naturalWidth", "naturalHeight", "isNaN", "processCanvasRGBA", "processCanvasRGB", "getImageDataFromCanvas", "topX", "topY", "getImageData", "e", "Error", "imageData", "processImageDataRGBA", "putImageData", "pixels", "data", "div", "widthMinus1", "heightMinus1", "radiusPlus1", "sumFactor", "stackStart", "BlurStack", "stack", "stackEnd", "i", "next", "stackIn", "stackOut", "yw", "yi", "mulSum", "shgSum", "y", "pr", "pg", "pb", "pa", "_i", "r", "g", "b", "a", "rInSum", "gInSum", "bInSum", "aInSum", "rOutSum", "gOutSum", "bOutSum", "aOutSum", "rSum", "gSum", "bSum", "aSum", "_i2", "p", "rbs", "x", "paInitial", "_a2", "_p", "_stackOut", "_r", "_g", "_b", "_a", "_x", "_pr", "_pg", "_pb", "_pa", "_rOutSum", "_gOutSum", "_bOutSum", "_aOutSum", "_rSum", "_gSum", "_bSum", "_aSum", "_i3", "yp", "_gInSum", "_bInSum", "_aInSum", "_rInSum", "_i4", "_rbs", "_y", "_p2", "processImageDataRGB", "_i5", "_i6", "_x2", "_pr2", "_pg2", "_pb2", "_rOutSum2", "_gOutSum2", "_bOutSum2", "_rSum2", "_gSum2", "_bSum2", "_i7", "_rInSum2", "_gInSum2", "_bInSum2", "_i8", "_y2", "canvasRGB", "canvasRGBA", "image", "imageDataRGB", "imageDataRGBA"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/stackblur-canvas/dist/stackblur-es.js"], "sourcesContent": ["function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null,\n      stackOut = null,\n      yw = 0,\n      yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        pa = pixels[yi + 3];\n\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0,\n        aInSum = 0,\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        aOutSum = radiusPlus1 * pa,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb,\n        aSum = sumFactor * pa;\n\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n          g = pixels[p + 1],\n          b = pixels[p + 2],\n          a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      var _p = x + radius + 1;\n\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n          _r = _stackOut.r,\n          _g = _stackOut.g,\n          _b = _stackOut.b,\n          _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n\n    var _pr = pixels[yi],\n        _pg = pixels[yi + 1],\n        _pb = pixels[yi + 2],\n        _pa = pixels[yi + 3],\n        _rOutSum = radiusPlus1 * _pr,\n        _gOutSum = radiusPlus1 * _pg,\n        _bOutSum = radiusPlus1 * _pb,\n        _aOutSum = radiusPlus1 * _pa,\n        _rSum = sumFactor * _pr,\n        _gSum = sumFactor * _pg,\n        _bSum = sumFactor * _pb,\n        _aSum = sumFactor * _pa;\n\n    stack = stackStart;\n\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n\n    var yp = width;\n    var _gInSum = 0,\n        _bInSum = 0,\n        _aInSum = 0,\n        _rInSum = 0;\n\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n\n      var _rbs = radiusPlus1 - _i4;\n\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n      yi = 0;\n\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb;\n    stack = stackStart;\n\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0;\n\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n\n    var _pr2 = pixels[yi],\n        _pg2 = pixels[yi + 1],\n        _pb2 = pixels[yi + 2],\n        _rOutSum2 = radiusPlus1 * _pr2,\n        _gOutSum2 = radiusPlus1 * _pg2,\n        _bOutSum2 = radiusPlus1 * _pb2,\n        _rSum2 = sumFactor * _pr2,\n        _gSum2 = sumFactor * _pg2,\n        _bSum2 = sumFactor * _pb2;\n\n    stack = stackStart;\n\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n\n    var _rInSum2 = 0,\n        _gInSum2 = 0,\n        _bInSum2 = 0;\n\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\nexport { BlurStack, processCanvasRGB as canvasRGB, processCanvasRGBA as canvasRGBA, processImage as image, processImageDataRGB as imageDataRGB, processImageDataRGBA as imageDataRGBA };\n"], "mappings": "AAAA,SAASA,OAAO,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,UAAUC,GAAG,EAAE;MACvB,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,UAAUC,GAAG,EAAE;MACvB,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASK,eAAe,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1wC,IAAIC,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC1gC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,YAAY,CAACC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAClF,IAAI,OAAOL,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAGM,QAAQ,CAACC,cAAc,CAACP,GAAG,CAAC;EACpC;EAEA,IAAI,CAACA,GAAG,IAAIQ,MAAM,CAAChB,SAAS,CAACiB,QAAQ,CAACC,IAAI,CAACV,GAAG,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,kBAAkB,IAAI,EAAE,cAAc,IAAIX,GAAG,CAAC,EAAE;IAC/G;EACF;EAEA,IAAIY,aAAa,GAAGR,SAAS,GAAG,QAAQ,GAAG,SAAS;EACpD,IAAIS,CAAC,GAAGb,GAAG,CAACY,aAAa,GAAG,OAAO,CAAC;EACpC,IAAIE,CAAC,GAAGd,GAAG,CAACY,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC;;EAEvC,IAAIJ,MAAM,CAAChB,SAAS,CAACiB,QAAQ,CAACC,IAAI,CAACV,GAAG,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,aAAa,EAAE;IACtEE,CAAC,GAAGb,GAAG,CAACe,KAAK;IACbD,CAAC,GAAGd,GAAG,CAACgB,MAAM;EAChB;EAEA,IAAI,OAAOf,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGK,QAAQ,CAACC,cAAc,CAACN,MAAM,CAAC;EAC1C;EAEA,IAAI,CAACA,MAAM,IAAI,EAAE,YAAY,IAAIA,MAAM,CAAC,EAAE;IACxC;EACF;EAEA,IAAI,CAACI,UAAU,EAAE;IACfJ,MAAM,CAACgB,KAAK,CAACF,KAAK,GAAGF,CAAC,GAAG,IAAI;IAC7BZ,MAAM,CAACgB,KAAK,CAACD,MAAM,GAAGF,CAAC,GAAG,IAAI;EAChC;EAEAb,MAAM,CAACc,KAAK,GAAGF,CAAC;EAChBZ,MAAM,CAACe,MAAM,GAAGF,CAAC;EACjB,IAAII,OAAO,GAAGjB,MAAM,CAACkB,UAAU,CAAC,IAAI,CAAC;EACrCD,OAAO,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEP,CAAC,EAAEC,CAAC,CAAC;EAC7BI,OAAO,CAACG,SAAS,CAACrB,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEA,GAAG,CAACsB,YAAY,EAAEtB,GAAG,CAACuB,aAAa,EAAE,CAAC,EAAE,CAAC,EAAEV,CAAC,EAAEC,CAAC,CAAC;EAE7E,IAAIU,KAAK,CAACtB,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;IAC/B;EACF;EAEA,IAAIC,gBAAgB,EAAE;IACpBsB,iBAAiB,CAACxB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEY,CAAC,EAAEC,CAAC,EAAEZ,MAAM,CAAC;EAC/C,CAAC,MAAM;IACLwB,gBAAgB,CAACzB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEY,CAAC,EAAEC,CAAC,EAAEZ,MAAM,CAAC;EAC9C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASyB,sBAAsB,CAAC1B,MAAM,EAAE2B,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAE;EACjE,IAAI,OAAOf,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGK,QAAQ,CAACC,cAAc,CAACN,MAAM,CAAC;EAC1C;EAEA,IAAI,CAACA,MAAM,IAAId,OAAO,CAACc,MAAM,CAAC,KAAK,QAAQ,IAAI,EAAE,YAAY,IAAIA,MAAM,CAAC,EAAE;IACxE,MAAM,IAAIL,SAAS,CAAC,4CAA4C,GAAG,+BAA+B,CAAC;EACrG;EAEA,IAAIsB,OAAO,GAAGjB,MAAM,CAACkB,UAAU,CAAC,IAAI,CAAC;EAErC,IAAI;IACF,OAAOD,OAAO,CAACY,YAAY,CAACF,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,CAAC;EACxD,CAAC,CAAC,OAAOe,CAAC,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,+BAA+B,GAAGD,CAAC,CAAC;EACtD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASN,iBAAiB,CAACxB,MAAM,EAAE2B,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAEd,MAAM,EAAE;EACpE,IAAIsB,KAAK,CAACtB,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;IAC/B;EACF;EAEAA,MAAM,IAAI,CAAC;EACX,IAAI+B,SAAS,GAAGN,sBAAsB,CAAC1B,MAAM,EAAE2B,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,CAAC;EACzEiB,SAAS,GAAGC,oBAAoB,CAACD,SAAS,EAAEL,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAEd,MAAM,CAAC;EAC9ED,MAAM,CAACkB,UAAU,CAAC,IAAI,CAAC,CAACgB,YAAY,CAACF,SAAS,EAAEL,IAAI,EAAEC,IAAI,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASK,oBAAoB,CAACD,SAAS,EAAEL,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAEd,MAAM,EAAE;EAC1E,IAAIkC,MAAM,GAAGH,SAAS,CAACI,IAAI;EAC3B,IAAIC,GAAG,GAAG,CAAC,GAAGpC,MAAM,GAAG,CAAC,CAAC,CAAC;;EAE1B,IAAIqC,WAAW,GAAGxB,KAAK,GAAG,CAAC;EAC3B,IAAIyB,YAAY,GAAGxB,MAAM,GAAG,CAAC;EAC7B,IAAIyB,WAAW,GAAGvC,MAAM,GAAG,CAAC;EAC5B,IAAIwC,SAAS,GAAGD,WAAW,IAAIA,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;EACnD,IAAIE,UAAU,GAAG,IAAIC,SAAS,EAAE;EAChC,IAAIC,KAAK,GAAGF,UAAU;EACtB,IAAIG,QAAQ;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,GAAG,EAAES,CAAC,EAAE,EAAE;IAC5BF,KAAK,GAAGA,KAAK,CAACG,IAAI,GAAG,IAAIJ,SAAS,EAAE;IAEpC,IAAIG,CAAC,KAAKN,WAAW,EAAE;MACrBK,QAAQ,GAAGD,KAAK;IAClB;EACF;EAEAA,KAAK,CAACG,IAAI,GAAGL,UAAU;EACvB,IAAIM,OAAO,GAAG,IAAI;IACdC,QAAQ,GAAG,IAAI;IACfC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;EACV,IAAIC,MAAM,GAAGxD,QAAQ,CAACK,MAAM,CAAC;EAC7B,IAAIoD,MAAM,GAAGxD,QAAQ,CAACI,MAAM,CAAC;EAE7B,KAAK,IAAIqD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,MAAM,EAAEuC,CAAC,EAAE,EAAE;IAC/BV,KAAK,GAAGF,UAAU;IAClB,IAAIa,EAAE,GAAGpB,MAAM,CAACgB,EAAE,CAAC;MACfK,EAAE,GAAGrB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACnBM,EAAE,GAAGtB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACnBO,EAAE,GAAGvB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;IAEvB,KAAK,IAAIQ,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGnB,WAAW,EAAEmB,EAAE,EAAE,EAAE;MACvCf,KAAK,CAACgB,CAAC,GAAGL,EAAE;MACZX,KAAK,CAACiB,CAAC,GAAGL,EAAE;MACZZ,KAAK,CAACkB,CAAC,GAAGL,EAAE;MACZb,KAAK,CAACmB,CAAC,GAAGL,EAAE;MACZd,KAAK,GAAGA,KAAK,CAACG,IAAI;IACpB;IAEA,IAAIiB,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;MACVC,OAAO,GAAG5B,WAAW,GAAGe,EAAE;MAC1Bc,OAAO,GAAG7B,WAAW,GAAGgB,EAAE;MAC1Bc,OAAO,GAAG9B,WAAW,GAAGiB,EAAE;MAC1Bc,OAAO,GAAG/B,WAAW,GAAGkB,EAAE;MAC1Bc,IAAI,GAAG/B,SAAS,GAAGc,EAAE;MACrBkB,IAAI,GAAGhC,SAAS,GAAGe,EAAE;MACrBkB,IAAI,GAAGjC,SAAS,GAAGgB,EAAE;MACrBkB,IAAI,GAAGlC,SAAS,GAAGiB,EAAE;IAEzB,KAAK,IAAIkB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpC,WAAW,EAAEoC,GAAG,EAAE,EAAE;MAC1C,IAAIC,CAAC,GAAG1B,EAAE,IAAI,CAACb,WAAW,GAAGsC,GAAG,GAAGtC,WAAW,GAAGsC,GAAG,KAAK,CAAC,CAAC;MAC3D,IAAIhB,CAAC,GAAGzB,MAAM,CAAC0C,CAAC,CAAC;QACbhB,CAAC,GAAG1B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;QACjBf,CAAC,GAAG3B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;QACjBd,CAAC,GAAG5B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;MACrB,IAAIC,GAAG,GAAGtC,WAAW,GAAGoC,GAAG;MAC3BJ,IAAI,IAAI,CAAC5B,KAAK,CAACgB,CAAC,GAAGA,CAAC,IAAIkB,GAAG;MAC3BL,IAAI,IAAI,CAAC7B,KAAK,CAACiB,CAAC,GAAGA,CAAC,IAAIiB,GAAG;MAC3BJ,IAAI,IAAI,CAAC9B,KAAK,CAACkB,CAAC,GAAGA,CAAC,IAAIgB,GAAG;MAC3BH,IAAI,IAAI,CAAC/B,KAAK,CAACmB,CAAC,GAAGA,CAAC,IAAIe,GAAG;MAC3Bd,MAAM,IAAIJ,CAAC;MACXK,MAAM,IAAIJ,CAAC;MACXK,MAAM,IAAIJ,CAAC;MACXK,MAAM,IAAIJ,CAAC;MACXnB,KAAK,GAAGA,KAAK,CAACG,IAAI;IACpB;IAEAC,OAAO,GAAGN,UAAU;IACpBO,QAAQ,GAAGJ,QAAQ;IAEnB,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,KAAK,EAAEiE,CAAC,EAAE,EAAE;MAC9B,IAAIC,SAAS,GAAGL,IAAI,GAAGvB,MAAM,KAAKC,MAAM;MACxClB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAG6B,SAAS;MAE1B,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnB,IAAIC,GAAG,GAAG,GAAG,GAAGD,SAAS;QAEzB7C,MAAM,CAACgB,EAAE,CAAC,GAAG,CAACqB,IAAI,GAAGpB,MAAM,KAAKC,MAAM,IAAI4B,GAAG;QAC7C9C,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAG,CAACsB,IAAI,GAAGrB,MAAM,KAAKC,MAAM,IAAI4B,GAAG;QACjD9C,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAG,CAACuB,IAAI,GAAGtB,MAAM,KAAKC,MAAM,IAAI4B,GAAG;MACnD,CAAC,MAAM;QACL9C,MAAM,CAACgB,EAAE,CAAC,GAAGhB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAGhB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;MAClD;MAEAqB,IAAI,IAAIJ,OAAO;MACfK,IAAI,IAAIJ,OAAO;MACfK,IAAI,IAAIJ,OAAO;MACfK,IAAI,IAAIJ,OAAO;MACfH,OAAO,IAAIpB,OAAO,CAACY,CAAC;MACpBS,OAAO,IAAIrB,OAAO,CAACa,CAAC;MACpBS,OAAO,IAAItB,OAAO,CAACc,CAAC;MACpBS,OAAO,IAAIvB,OAAO,CAACe,CAAC;MAEpB,IAAImB,EAAE,GAAGH,CAAC,GAAG9E,MAAM,GAAG,CAAC;MAEvBiF,EAAE,GAAGhC,EAAE,IAAIgC,EAAE,GAAG5C,WAAW,GAAG4C,EAAE,GAAG5C,WAAW,CAAC,IAAI,CAAC;MACpD0B,MAAM,IAAIhB,OAAO,CAACY,CAAC,GAAGzB,MAAM,CAAC+C,EAAE,CAAC;MAChCjB,MAAM,IAAIjB,OAAO,CAACa,CAAC,GAAG1B,MAAM,CAAC+C,EAAE,GAAG,CAAC,CAAC;MACpChB,MAAM,IAAIlB,OAAO,CAACc,CAAC,GAAG3B,MAAM,CAAC+C,EAAE,GAAG,CAAC,CAAC;MACpCf,MAAM,IAAInB,OAAO,CAACe,CAAC,GAAG5B,MAAM,CAAC+C,EAAE,GAAG,CAAC,CAAC;MACpCV,IAAI,IAAIR,MAAM;MACdS,IAAI,IAAIR,MAAM;MACdS,IAAI,IAAIR,MAAM;MACdS,IAAI,IAAIR,MAAM;MACdnB,OAAO,GAAGA,OAAO,CAACD,IAAI;MACtB,IAAIoC,SAAS,GAAGlC,QAAQ;QACpBmC,EAAE,GAAGD,SAAS,CAACvB,CAAC;QAChByB,EAAE,GAAGF,SAAS,CAACtB,CAAC;QAChByB,EAAE,GAAGH,SAAS,CAACrB,CAAC;QAChByB,EAAE,GAAGJ,SAAS,CAACpB,CAAC;MACpBK,OAAO,IAAIgB,EAAE;MACbf,OAAO,IAAIgB,EAAE;MACbf,OAAO,IAAIgB,EAAE;MACbf,OAAO,IAAIgB,EAAE;MACbvB,MAAM,IAAIoB,EAAE;MACZnB,MAAM,IAAIoB,EAAE;MACZnB,MAAM,IAAIoB,EAAE;MACZnB,MAAM,IAAIoB,EAAE;MACZtC,QAAQ,GAAGA,QAAQ,CAACF,IAAI;MACxBI,EAAE,IAAI,CAAC;IACT;IAEAD,EAAE,IAAIpC,KAAK;EACb;EAEA,KAAK,IAAI0E,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG1E,KAAK,EAAE0E,EAAE,EAAE,EAAE;IACjCrC,EAAE,GAAGqC,EAAE,IAAI,CAAC;IAEZ,IAAIC,GAAG,GAAGtD,MAAM,CAACgB,EAAE,CAAC;MAChBuC,GAAG,GAAGvD,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACpBwC,GAAG,GAAGxD,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACpByC,GAAG,GAAGzD,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACpB0C,QAAQ,GAAGrD,WAAW,GAAGiD,GAAG;MAC5BK,QAAQ,GAAGtD,WAAW,GAAGkD,GAAG;MAC5BK,QAAQ,GAAGvD,WAAW,GAAGmD,GAAG;MAC5BK,QAAQ,GAAGxD,WAAW,GAAGoD,GAAG;MAC5BK,KAAK,GAAGxD,SAAS,GAAGgD,GAAG;MACvBS,KAAK,GAAGzD,SAAS,GAAGiD,GAAG;MACvBS,KAAK,GAAG1D,SAAS,GAAGkD,GAAG;MACvBS,KAAK,GAAG3D,SAAS,GAAGmD,GAAG;IAE3BhD,KAAK,GAAGF,UAAU;IAElB,KAAK,IAAI2D,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG7D,WAAW,EAAE6D,GAAG,EAAE,EAAE;MAC1CzD,KAAK,CAACgB,CAAC,GAAG6B,GAAG;MACb7C,KAAK,CAACiB,CAAC,GAAG6B,GAAG;MACb9C,KAAK,CAACkB,CAAC,GAAG6B,GAAG;MACb/C,KAAK,CAACmB,CAAC,GAAG6B,GAAG;MACbhD,KAAK,GAAGA,KAAK,CAACG,IAAI;IACpB;IAEA,IAAIuD,EAAE,GAAGxF,KAAK;IACd,IAAIyF,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAAC;MACXC,OAAO,GAAG,CAAC;IAEf,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI1G,MAAM,EAAE0G,GAAG,EAAE,EAAE;MACtCxD,EAAE,GAAGmD,EAAE,GAAGd,EAAE,IAAI,CAAC;MAEjB,IAAIoB,IAAI,GAAGpE,WAAW,GAAGmE,GAAG;MAE5BV,KAAK,IAAI,CAACrD,KAAK,CAACgB,CAAC,GAAG6B,GAAG,GAAGtD,MAAM,CAACgB,EAAE,CAAC,IAAIyD,IAAI;MAC5CV,KAAK,IAAI,CAACtD,KAAK,CAACiB,CAAC,GAAG6B,GAAG,GAAGvD,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,IAAIyD,IAAI;MAChDT,KAAK,IAAI,CAACvD,KAAK,CAACkB,CAAC,GAAG6B,GAAG,GAAGxD,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,IAAIyD,IAAI;MAChDR,KAAK,IAAI,CAACxD,KAAK,CAACmB,CAAC,GAAG6B,GAAG,GAAGzD,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,IAAIyD,IAAI;MAChDF,OAAO,IAAIjB,GAAG;MACdc,OAAO,IAAIb,GAAG;MACdc,OAAO,IAAIb,GAAG;MACdc,OAAO,IAAIb,GAAG;MACdhD,KAAK,GAAGA,KAAK,CAACG,IAAI;MAElB,IAAI4D,GAAG,GAAGpE,YAAY,EAAE;QACtB+D,EAAE,IAAIxF,KAAK;MACb;IACF;IAEAqC,EAAE,GAAGqC,EAAE;IACPxC,OAAO,GAAGN,UAAU;IACpBO,QAAQ,GAAGJ,QAAQ;IAEnB,KAAK,IAAIgE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG9F,MAAM,EAAE8F,EAAE,EAAE,EAAE;MAClC,IAAIC,GAAG,GAAG3D,EAAE,IAAI,CAAC;MAEjBhB,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC,GAAGlB,GAAG,GAAGQ,KAAK,GAAGhD,MAAM,KAAKC,MAAM;MAEjD,IAAIuC,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAG,GAAG,GAAGA,GAAG;QACfzD,MAAM,CAAC2E,GAAG,CAAC,GAAG,CAACb,KAAK,GAAG7C,MAAM,KAAKC,MAAM,IAAIuC,GAAG;QAC/CzD,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC,GAAG,CAACZ,KAAK,GAAG9C,MAAM,KAAKC,MAAM,IAAIuC,GAAG;QACnDzD,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC,GAAG,CAACX,KAAK,GAAG/C,MAAM,KAAKC,MAAM,IAAIuC,GAAG;MACrD,CAAC,MAAM;QACLzD,MAAM,CAAC2E,GAAG,CAAC,GAAG3E,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC,GAAG3E,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;MACrD;MAEAb,KAAK,IAAIJ,QAAQ;MACjBK,KAAK,IAAIJ,QAAQ;MACjBK,KAAK,IAAIJ,QAAQ;MACjBK,KAAK,IAAIJ,QAAQ;MACjBH,QAAQ,IAAI7C,OAAO,CAACY,CAAC;MACrBkC,QAAQ,IAAI9C,OAAO,CAACa,CAAC;MACrBkC,QAAQ,IAAI/C,OAAO,CAACc,CAAC;MACrBkC,QAAQ,IAAIhD,OAAO,CAACe,CAAC;MACrB+C,GAAG,GAAGtB,EAAE,GAAG,CAAC,CAACsB,GAAG,GAAGD,EAAE,GAAGrE,WAAW,IAAID,YAAY,GAAGuE,GAAG,GAAGvE,YAAY,IAAIzB,KAAK,IAAI,CAAC;MACtFmF,KAAK,IAAIS,OAAO,IAAI1D,OAAO,CAACY,CAAC,GAAGzB,MAAM,CAAC2E,GAAG,CAAC;MAC3CZ,KAAK,IAAIK,OAAO,IAAIvD,OAAO,CAACa,CAAC,GAAG1B,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC;MAC/CX,KAAK,IAAIK,OAAO,IAAIxD,OAAO,CAACc,CAAC,GAAG3B,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC;MAC/CV,KAAK,IAAIK,OAAO,IAAIzD,OAAO,CAACe,CAAC,GAAG5B,MAAM,CAAC2E,GAAG,GAAG,CAAC,CAAC;MAC/C9D,OAAO,GAAGA,OAAO,CAACD,IAAI;MACtB8C,QAAQ,IAAIJ,GAAG,GAAGxC,QAAQ,CAACW,CAAC;MAC5BkC,QAAQ,IAAIJ,GAAG,GAAGzC,QAAQ,CAACY,CAAC;MAC5BkC,QAAQ,IAAIJ,GAAG,GAAG1C,QAAQ,CAACa,CAAC;MAC5BkC,QAAQ,IAAIJ,GAAG,GAAG3C,QAAQ,CAACc,CAAC;MAC5B2C,OAAO,IAAIjB,GAAG;MACdc,OAAO,IAAIb,GAAG;MACdc,OAAO,IAAIb,GAAG;MACdc,OAAO,IAAIb,GAAG;MACd3C,QAAQ,GAAGA,QAAQ,CAACF,IAAI;MACxBI,EAAE,IAAIrC,KAAK;IACb;EACF;EAEA,OAAOkB,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASP,gBAAgB,CAACzB,MAAM,EAAE2B,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAEd,MAAM,EAAE;EACnE,IAAIsB,KAAK,CAACtB,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;IAC/B;EACF;EAEAA,MAAM,IAAI,CAAC;EACX,IAAI+B,SAAS,GAAGN,sBAAsB,CAAC1B,MAAM,EAAE2B,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,CAAC;EACzEiB,SAAS,GAAG+E,mBAAmB,CAAC/E,SAAS,EAAEL,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAEd,MAAM,CAAC;EAC7ED,MAAM,CAACkB,UAAU,CAAC,IAAI,CAAC,CAACgB,YAAY,CAACF,SAAS,EAAEL,IAAI,EAAEC,IAAI,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASmF,mBAAmB,CAAC/E,SAAS,EAAEL,IAAI,EAAEC,IAAI,EAAEd,KAAK,EAAEC,MAAM,EAAEd,MAAM,EAAE;EACzE,IAAIkC,MAAM,GAAGH,SAAS,CAACI,IAAI;EAC3B,IAAIC,GAAG,GAAG,CAAC,GAAGpC,MAAM,GAAG,CAAC,CAAC,CAAC;;EAE1B,IAAIqC,WAAW,GAAGxB,KAAK,GAAG,CAAC;EAC3B,IAAIyB,YAAY,GAAGxB,MAAM,GAAG,CAAC;EAC7B,IAAIyB,WAAW,GAAGvC,MAAM,GAAG,CAAC;EAC5B,IAAIwC,SAAS,GAAGD,WAAW,IAAIA,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;EACnD,IAAIE,UAAU,GAAG,IAAIC,SAAS,EAAE;EAChC,IAAIC,KAAK,GAAGF,UAAU;EACtB,IAAIG,QAAQ;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,GAAG,EAAES,CAAC,EAAE,EAAE;IAC5BF,KAAK,GAAGA,KAAK,CAACG,IAAI,GAAG,IAAIJ,SAAS,EAAE;IAEpC,IAAIG,CAAC,KAAKN,WAAW,EAAE;MACrBK,QAAQ,GAAGD,KAAK;IAClB;EACF;EAEAA,KAAK,CAACG,IAAI,GAAGL,UAAU;EACvB,IAAIM,OAAO,GAAG,IAAI;EAClB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIG,MAAM,GAAGxD,QAAQ,CAACK,MAAM,CAAC;EAC7B,IAAIoD,MAAM,GAAGxD,QAAQ,CAACI,MAAM,CAAC;EAC7B,IAAI4E,CAAC,EAAEC,GAAG;EACV,IAAI5B,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;EAEV,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,MAAM,EAAEuC,CAAC,EAAE,EAAE;IAC/B,IAAIC,EAAE,GAAGpB,MAAM,CAACgB,EAAE,CAAC;MACfK,EAAE,GAAGrB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACnBM,EAAE,GAAGtB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACnBiB,OAAO,GAAG5B,WAAW,GAAGe,EAAE;MAC1Bc,OAAO,GAAG7B,WAAW,GAAGgB,EAAE;MAC1Bc,OAAO,GAAG9B,WAAW,GAAGiB,EAAE;MAC1Be,IAAI,GAAG/B,SAAS,GAAGc,EAAE;MACrBkB,IAAI,GAAGhC,SAAS,GAAGe,EAAE;MACrBkB,IAAI,GAAGjC,SAAS,GAAGgB,EAAE;IACzBb,KAAK,GAAGF,UAAU;IAElB,KAAK,IAAIsE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGxE,WAAW,EAAEwE,GAAG,EAAE,EAAE;MAC1CpE,KAAK,CAACgB,CAAC,GAAGL,EAAE;MACZX,KAAK,CAACiB,CAAC,GAAGL,EAAE;MACZZ,KAAK,CAACkB,CAAC,GAAGL,EAAE;MACZb,KAAK,GAAGA,KAAK,CAACG,IAAI;IACpB;IAEA,IAAIiB,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;IAEd,KAAK,IAAI+C,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGzE,WAAW,EAAEyE,GAAG,EAAE,EAAE;MAC1CpC,CAAC,GAAG1B,EAAE,IAAI,CAACb,WAAW,GAAG2E,GAAG,GAAG3E,WAAW,GAAG2E,GAAG,KAAK,CAAC,CAAC;MACvDzC,IAAI,IAAI,CAAC5B,KAAK,CAACgB,CAAC,GAAGL,EAAE,GAAGpB,MAAM,CAAC0C,CAAC,CAAC,KAAKC,GAAG,GAAGtC,WAAW,GAAGyE,GAAG,CAAC;MAC9DxC,IAAI,IAAI,CAAC7B,KAAK,CAACiB,CAAC,GAAGL,EAAE,GAAGrB,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC,IAAIC,GAAG;MAC5CJ,IAAI,IAAI,CAAC9B,KAAK,CAACkB,CAAC,GAAGL,EAAE,GAAGtB,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC,IAAIC,GAAG;MAC5Cd,MAAM,IAAIT,EAAE;MACZU,MAAM,IAAIT,EAAE;MACZU,MAAM,IAAIT,EAAE;MACZb,KAAK,GAAGA,KAAK,CAACG,IAAI;IACpB;IAEAC,OAAO,GAAGN,UAAU;IACpBO,QAAQ,GAAGJ,QAAQ;IAEnB,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,KAAK,EAAEiE,CAAC,EAAE,EAAE;MAC9B5C,MAAM,CAACgB,EAAE,CAAC,GAAGqB,IAAI,GAAGpB,MAAM,KAAKC,MAAM;MACrClB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAGsB,IAAI,GAAGrB,MAAM,KAAKC,MAAM;MACzClB,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,GAAGuB,IAAI,GAAGtB,MAAM,KAAKC,MAAM;MACzCmB,IAAI,IAAIJ,OAAO;MACfK,IAAI,IAAIJ,OAAO;MACfK,IAAI,IAAIJ,OAAO;MACfF,OAAO,IAAIpB,OAAO,CAACY,CAAC;MACpBS,OAAO,IAAIrB,OAAO,CAACa,CAAC;MACpBS,OAAO,IAAItB,OAAO,CAACc,CAAC;MACpBe,CAAC,GAAG3B,EAAE,IAAI,CAAC2B,CAAC,GAAGE,CAAC,GAAG9E,MAAM,GAAG,CAAC,IAAIqC,WAAW,GAAGuC,CAAC,GAAGvC,WAAW,CAAC,IAAI,CAAC;MACpE0B,MAAM,IAAIhB,OAAO,CAACY,CAAC,GAAGzB,MAAM,CAAC0C,CAAC,CAAC;MAC/BZ,MAAM,IAAIjB,OAAO,CAACa,CAAC,GAAG1B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;MACnCX,MAAM,IAAIlB,OAAO,CAACc,CAAC,GAAG3B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;MACnCL,IAAI,IAAIR,MAAM;MACdS,IAAI,IAAIR,MAAM;MACdS,IAAI,IAAIR,MAAM;MACdlB,OAAO,GAAGA,OAAO,CAACD,IAAI;MACtBqB,OAAO,IAAIb,EAAE,GAAGN,QAAQ,CAACW,CAAC;MAC1BS,OAAO,IAAIb,EAAE,GAAGP,QAAQ,CAACY,CAAC;MAC1BS,OAAO,IAAIb,EAAE,GAAGR,QAAQ,CAACa,CAAC;MAC1BE,MAAM,IAAIT,EAAE;MACZU,MAAM,IAAIT,EAAE;MACZU,MAAM,IAAIT,EAAE;MACZR,QAAQ,GAAGA,QAAQ,CAACF,IAAI;MACxBI,EAAE,IAAI,CAAC;IACT;IAEAD,EAAE,IAAIpC,KAAK;EACb;EAEA,KAAK,IAAIoG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpG,KAAK,EAAEoG,GAAG,EAAE,EAAE;IACpC/D,EAAE,GAAG+D,GAAG,IAAI,CAAC;IAEb,IAAIC,IAAI,GAAGhF,MAAM,CAACgB,EAAE,CAAC;MACjBiE,IAAI,GAAGjF,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACrBkE,IAAI,GAAGlF,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC;MACrBmE,SAAS,GAAG9E,WAAW,GAAG2E,IAAI;MAC9BI,SAAS,GAAG/E,WAAW,GAAG4E,IAAI;MAC9BI,SAAS,GAAGhF,WAAW,GAAG6E,IAAI;MAC9BI,MAAM,GAAGhF,SAAS,GAAG0E,IAAI;MACzBO,MAAM,GAAGjF,SAAS,GAAG2E,IAAI;MACzBO,MAAM,GAAGlF,SAAS,GAAG4E,IAAI;IAE7BzE,KAAK,GAAGF,UAAU;IAElB,KAAK,IAAIkF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpF,WAAW,EAAEoF,GAAG,EAAE,EAAE;MAC1ChF,KAAK,CAACgB,CAAC,GAAGuD,IAAI;MACdvE,KAAK,CAACiB,CAAC,GAAGuD,IAAI;MACdxE,KAAK,CAACkB,CAAC,GAAGuD,IAAI;MACdzE,KAAK,GAAGA,KAAK,CAACG,IAAI;IACpB;IAEA,IAAI8E,QAAQ,GAAG,CAAC;MACZC,QAAQ,GAAG,CAAC;MACZC,QAAQ,GAAG,CAAC;IAEhB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAE1B,EAAE,GAAGxF,KAAK,EAAEkH,GAAG,IAAI/H,MAAM,EAAE+H,GAAG,EAAE,EAAE;MAClD7E,EAAE,GAAGmD,EAAE,GAAGY,GAAG,IAAI,CAAC;MAClBO,MAAM,IAAI,CAAC7E,KAAK,CAACgB,CAAC,GAAGuD,IAAI,GAAGhF,MAAM,CAACgB,EAAE,CAAC,KAAK2B,GAAG,GAAGtC,WAAW,GAAGwF,GAAG,CAAC;MACnEN,MAAM,IAAI,CAAC9E,KAAK,CAACiB,CAAC,GAAGuD,IAAI,GAAGjF,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,IAAI2B,GAAG;MACjD6C,MAAM,IAAI,CAAC/E,KAAK,CAACkB,CAAC,GAAGuD,IAAI,GAAGlF,MAAM,CAACgB,EAAE,GAAG,CAAC,CAAC,IAAI2B,GAAG;MACjD+C,QAAQ,IAAIV,IAAI;MAChBW,QAAQ,IAAIV,IAAI;MAChBW,QAAQ,IAAIV,IAAI;MAChBzE,KAAK,GAAGA,KAAK,CAACG,IAAI;MAElB,IAAIiF,GAAG,GAAGzF,YAAY,EAAE;QACtB+D,EAAE,IAAIxF,KAAK;MACb;IACF;IAEAqC,EAAE,GAAG+D,GAAG;IACRlE,OAAO,GAAGN,UAAU;IACpBO,QAAQ,GAAGJ,QAAQ;IAEnB,KAAK,IAAIoF,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGlH,MAAM,EAAEkH,GAAG,EAAE,EAAE;MACrCpD,CAAC,GAAG1B,EAAE,IAAI,CAAC;MACXhB,MAAM,CAAC0C,CAAC,CAAC,GAAG4C,MAAM,GAAGrE,MAAM,KAAKC,MAAM;MACtClB,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC,GAAG6C,MAAM,GAAGtE,MAAM,KAAKC,MAAM;MAC1ClB,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC,GAAG8C,MAAM,GAAGvE,MAAM,KAAKC,MAAM;MAC1CoE,MAAM,IAAIH,SAAS;MACnBI,MAAM,IAAIH,SAAS;MACnBI,MAAM,IAAIH,SAAS;MACnBF,SAAS,IAAItE,OAAO,CAACY,CAAC;MACtB2D,SAAS,IAAIvE,OAAO,CAACa,CAAC;MACtB2D,SAAS,IAAIxE,OAAO,CAACc,CAAC;MACtBe,CAAC,GAAGqC,GAAG,GAAG,CAAC,CAACrC,CAAC,GAAGoD,GAAG,GAAGzF,WAAW,IAAID,YAAY,GAAGsC,CAAC,GAAGtC,YAAY,IAAIzB,KAAK,IAAI,CAAC;MAClF2G,MAAM,IAAII,QAAQ,IAAI7E,OAAO,CAACY,CAAC,GAAGzB,MAAM,CAAC0C,CAAC,CAAC;MAC3C6C,MAAM,IAAII,QAAQ,IAAI9E,OAAO,CAACa,CAAC,GAAG1B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;MAC/C8C,MAAM,IAAII,QAAQ,IAAI/E,OAAO,CAACc,CAAC,GAAG3B,MAAM,CAAC0C,CAAC,GAAG,CAAC,CAAC;MAC/C7B,OAAO,GAAGA,OAAO,CAACD,IAAI;MACtBuE,SAAS,IAAIH,IAAI,GAAGlE,QAAQ,CAACW,CAAC;MAC9B2D,SAAS,IAAIH,IAAI,GAAGnE,QAAQ,CAACY,CAAC;MAC9B2D,SAAS,IAAIH,IAAI,GAAGpE,QAAQ,CAACa,CAAC;MAC9B+D,QAAQ,IAAIV,IAAI;MAChBW,QAAQ,IAAIV,IAAI;MAChBW,QAAQ,IAAIV,IAAI;MAChBpE,QAAQ,GAAGA,QAAQ,CAACF,IAAI;MACxBI,EAAE,IAAIrC,KAAK;IACb;EACF;EAEA,OAAOkB,SAAS;AAClB;AACA;AACA;AACA;;AAGA,IAAIW,SAAS;AACb;AACA;AACA;AACA,SAASA,SAAS,GAAG;EACnBnD,eAAe,CAAC,IAAI,EAAEmD,SAAS,CAAC;EAEhC,IAAI,CAACiB,CAAC,GAAG,CAAC;EACV,IAAI,CAACC,CAAC,GAAG,CAAC;EACV,IAAI,CAACC,CAAC,GAAG,CAAC;EACV,IAAI,CAACC,CAAC,GAAG,CAAC;EACV,IAAI,CAAChB,IAAI,GAAG,IAAI;AAClB,CAAC;AAED,SAASJ,SAAS,EAAElB,gBAAgB,IAAIyG,SAAS,EAAE1G,iBAAiB,IAAI2G,UAAU,EAAErI,YAAY,IAAIsI,KAAK,EAAErB,mBAAmB,IAAIsB,YAAY,EAAEpG,oBAAoB,IAAIqG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}