{"ast": null, "code": "import createToPairs from './_createToPairs.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Creates an array of own and inherited enumerable string keyed-value pairs\n * for `object` which can be consumed by `_.fromPairs`. If `object` is a map\n * or set, its entries are returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @alias entriesIn\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the key-value pairs.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.toPairsIn(new Foo);\n * // => [['a', 1], ['b', 2], ['c', 3]] (iteration order is not guaranteed)\n */\nvar toPairsIn = createToPairs(keysIn);\nexport default toPairsIn;", "map": {"version": 3, "names": ["createToPairs", "keysIn", "toPairsIn"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/toPairsIn.js"], "sourcesContent": ["import createToPairs from './_createToPairs.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Creates an array of own and inherited enumerable string keyed-value pairs\n * for `object` which can be consumed by `_.fromPairs`. If `object` is a map\n * or set, its entries are returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @alias entriesIn\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the key-value pairs.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.toPairsIn(new Foo);\n * // => [['a', 1], ['b', 2], ['c', 3]] (iteration order is not guaranteed)\n */\nvar toPairsIn = createToPairs(keysIn);\n\nexport default toPairsIn;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGF,aAAa,CAACC,MAAM,CAAC;AAErC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}