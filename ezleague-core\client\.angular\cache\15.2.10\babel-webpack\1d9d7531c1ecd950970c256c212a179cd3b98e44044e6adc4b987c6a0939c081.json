{"ast": null, "code": "/**\n * This method returns an empty string.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {string} Returns the empty string.\n * @example\n *\n * _.times(2, _.stubString);\n * // => ['', '']\n */\nfunction stubString() {\n  return '';\n}\nexport default stubString;", "map": {"version": 3, "names": ["stubString"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/stubString.js"], "sourcesContent": ["/**\n * This method returns an empty string.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {string} Returns the empty string.\n * @example\n *\n * _.times(2, _.stubString);\n * // => ['', '']\n */\nfunction stubString() {\n  return '';\n}\n\nexport default stubString;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,GAAG;EACpB,OAAO,EAAE;AACX;AAEA,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}