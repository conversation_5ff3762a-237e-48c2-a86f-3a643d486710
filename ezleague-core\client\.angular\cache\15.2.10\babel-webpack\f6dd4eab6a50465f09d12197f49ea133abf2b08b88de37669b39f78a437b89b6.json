{"ast": null, "code": "import root from './_root.js';\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\nexport default Symbol;", "map": {"version": 3, "names": ["root", "Symbol"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_Symbol.js"], "sourcesContent": ["import root from './_root.js';\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nexport default Symbol;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;AAExB,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}