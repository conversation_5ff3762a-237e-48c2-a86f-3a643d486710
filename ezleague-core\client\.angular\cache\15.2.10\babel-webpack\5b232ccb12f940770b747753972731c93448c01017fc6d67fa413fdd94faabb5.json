{"ast": null, "code": "import isIndex from './_isIndex.js';\n\n/**\n * The base implementation of `_.nth` which doesn't coerce arguments.\n *\n * @private\n * @param {Array} array The array to query.\n * @param {number} n The index of the element to return.\n * @returns {*} Returns the nth element of `array`.\n */\nfunction baseNth(array, n) {\n  var length = array.length;\n  if (!length) {\n    return;\n  }\n  n += n < 0 ? length : 0;\n  return isIndex(n, length) ? array[n] : undefined;\n}\nexport default baseNth;", "map": {"version": 3, "names": ["isIndex", "baseNth", "array", "n", "length", "undefined"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_baseNth.js"], "sourcesContent": ["import isIndex from './_isIndex.js';\n\n/**\n * The base implementation of `_.nth` which doesn't coerce arguments.\n *\n * @private\n * @param {Array} array The array to query.\n * @param {number} n The index of the element to return.\n * @returns {*} Returns the nth element of `array`.\n */\nfunction baseNth(array, n) {\n  var length = array.length;\n  if (!length) {\n    return;\n  }\n  n += n < 0 ? length : 0;\n  return isIndex(n, length) ? array[n] : undefined;\n}\n\nexport default baseNth;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAO,CAACC,KAAK,EAAEC,CAAC,EAAE;EACzB,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;EACzB,IAAI,CAACA,MAAM,EAAE;IACX;EACF;EACAD,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGC,MAAM,GAAG,CAAC;EACvB,OAAOJ,OAAO,CAACG,CAAC,EAAEC,MAAM,CAAC,GAAGF,KAAK,CAACC,CAAC,CAAC,GAAGE,SAAS;AAClD;AAEA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}