{"ast": null, "code": "import arrayEach from './_arrayEach.js';\nimport arrayIncludes from './_arrayIncludes.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_CURRY_FLAG = 8,\n  WRAP_CURRY_RIGHT_FLAG = 16,\n  WRAP_PARTIAL_FLAG = 32,\n  WRAP_PARTIAL_RIGHT_FLAG = 64,\n  WRAP_ARY_FLAG = 128,\n  WRAP_REARG_FLAG = 256,\n  WRAP_FLIP_FLAG = 512;\n\n/** Used to associate wrap methods with their bit flags. */\nvar wrapFlags = [['ary', WRAP_ARY_FLAG], ['bind', WRAP_BIND_FLAG], ['bindKey', WRAP_BIND_KEY_FLAG], ['curry', WRAP_CURRY_FLAG], ['curryRight', WRAP_CURRY_RIGHT_FLAG], ['flip', WRAP_FLIP_FLAG], ['partial', WRAP_PARTIAL_FLAG], ['partialRight', WRAP_PARTIAL_RIGHT_FLAG], ['rearg', WRAP_REARG_FLAG]];\n\n/**\n * Updates wrapper `details` based on `bitmask` flags.\n *\n * @private\n * @returns {Array} details The details to modify.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @returns {Array} Returns `details`.\n */\nfunction updateWrapDetails(details, bitmask) {\n  arrayEach(wrapFlags, function (pair) {\n    var value = '_.' + pair[0];\n    if (bitmask & pair[1] && !arrayIncludes(details, value)) {\n      details.push(value);\n    }\n  });\n  return details.sort();\n}\nexport default updateWrapDetails;", "map": {"version": 3, "names": ["arrayEach", "arrayIncludes", "WRAP_BIND_FLAG", "WRAP_BIND_KEY_FLAG", "WRAP_CURRY_FLAG", "WRAP_CURRY_RIGHT_FLAG", "WRAP_PARTIAL_FLAG", "WRAP_PARTIAL_RIGHT_FLAG", "WRAP_ARY_FLAG", "WRAP_REARG_FLAG", "WRAP_FLIP_FLAG", "wrapFlags", "updateWrapDetails", "details", "bitmask", "pair", "value", "push", "sort"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_updateWrapDetails.js"], "sourcesContent": ["import arrayEach from './_arrayEach.js';\nimport arrayIncludes from './_arrayIncludes.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_CURRY_FLAG = 8,\n    WRAP_CURRY_RIGHT_FLAG = 16,\n    WRAP_PARTIAL_FLAG = 32,\n    WRAP_PARTIAL_RIGHT_FLAG = 64,\n    WRAP_ARY_FLAG = 128,\n    WRAP_REARG_FLAG = 256,\n    WRAP_FLIP_FLAG = 512;\n\n/** Used to associate wrap methods with their bit flags. */\nvar wrapFlags = [\n  ['ary', WRAP_ARY_FLAG],\n  ['bind', WRAP_BIND_FLAG],\n  ['bindKey', WRAP_BIND_KEY_FLAG],\n  ['curry', WRAP_CURRY_FLAG],\n  ['curryRight', WRAP_CURRY_RIGHT_FLAG],\n  ['flip', WRAP_FLIP_FLAG],\n  ['partial', WRAP_PARTIAL_FLAG],\n  ['partialRight', WRAP_PARTIAL_RIGHT_FLAG],\n  ['rearg', WRAP_REARG_FLAG]\n];\n\n/**\n * Updates wrapper `details` based on `bitmask` flags.\n *\n * @private\n * @returns {Array} details The details to modify.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @returns {Array} Returns `details`.\n */\nfunction updateWrapDetails(details, bitmask) {\n  arrayEach(wrapFlags, function(pair) {\n    var value = '_.' + pair[0];\n    if ((bitmask & pair[1]) && !arrayIncludes(details, value)) {\n      details.push(value);\n    }\n  });\n  return details.sort();\n}\n\nexport default updateWrapDetails;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,kBAAkB,GAAG,CAAC;EACtBC,eAAe,GAAG,CAAC;EACnBC,qBAAqB,GAAG,EAAE;EAC1BC,iBAAiB,GAAG,EAAE;EACtBC,uBAAuB,GAAG,EAAE;EAC5BC,aAAa,GAAG,GAAG;EACnBC,eAAe,GAAG,GAAG;EACrBC,cAAc,GAAG,GAAG;;AAExB;AACA,IAAIC,SAAS,GAAG,CACd,CAAC,KAAK,EAAEH,aAAa,CAAC,EACtB,CAAC,MAAM,EAAEN,cAAc,CAAC,EACxB,CAAC,SAAS,EAAEC,kBAAkB,CAAC,EAC/B,CAAC,OAAO,EAAEC,eAAe,CAAC,EAC1B,CAAC,YAAY,EAAEC,qBAAqB,CAAC,EACrC,CAAC,MAAM,EAAEK,cAAc,CAAC,EACxB,CAAC,SAAS,EAAEJ,iBAAiB,CAAC,EAC9B,CAAC,cAAc,EAAEC,uBAAuB,CAAC,EACzC,CAAC,OAAO,EAAEE,eAAe,CAAC,CAC3B;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiB,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC3Cd,SAAS,CAACW,SAAS,EAAE,UAASI,IAAI,EAAE;IAClC,IAAIC,KAAK,GAAG,IAAI,GAAGD,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAKD,OAAO,GAAGC,IAAI,CAAC,CAAC,CAAC,IAAK,CAACd,aAAa,CAACY,OAAO,EAAEG,KAAK,CAAC,EAAE;MACzDH,OAAO,CAACI,IAAI,CAACD,KAAK,CAAC;IACrB;EACF,CAAC,CAAC;EACF,OAAOH,OAAO,CAACK,IAAI,EAAE;AACvB;AAEA,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}