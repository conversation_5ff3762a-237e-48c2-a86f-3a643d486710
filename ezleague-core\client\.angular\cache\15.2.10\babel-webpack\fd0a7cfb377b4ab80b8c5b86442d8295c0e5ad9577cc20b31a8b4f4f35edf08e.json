{"ast": null, "code": "'use strict';\n\nmodule.exports = function encodeUtf8(input) {\n  var result = [];\n  var size = input.length;\n  for (var index = 0; index < size; index++) {\n    var point = input.charCodeAt(index);\n    if (point >= 0xD800 && point <= 0xDBFF && size > index + 1) {\n      var second = input.charCodeAt(index + 1);\n      if (second >= 0xDC00 && second <= 0xDFFF) {\n        // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        point = (point - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n        index += 1;\n      }\n    }\n\n    // US-ASCII\n    if (point < 0x80) {\n      result.push(point);\n      continue;\n    }\n\n    // 2-byte UTF-8\n    if (point < 0x800) {\n      result.push(point >> 6 | 192);\n      result.push(point & 63 | 128);\n      continue;\n    }\n\n    // 3-byte UTF-8\n    if (point < 0xD800 || point >= 0xE000 && point < 0x10000) {\n      result.push(point >> 12 | 224);\n      result.push(point >> 6 & 63 | 128);\n      result.push(point & 63 | 128);\n      continue;\n    }\n\n    // 4-byte UTF-8\n    if (point >= 0x10000 && point <= 0x10FFFF) {\n      result.push(point >> 18 | 240);\n      result.push(point >> 12 & 63 | 128);\n      result.push(point >> 6 & 63 | 128);\n      result.push(point & 63 | 128);\n      continue;\n    }\n\n    // Invalid character\n    result.push(0xEF, 0xBF, 0xBD);\n  }\n  return new Uint8Array(result).buffer;\n};", "map": {"version": 3, "names": ["module", "exports", "encodeUtf8", "input", "result", "size", "length", "index", "point", "charCodeAt", "second", "push", "Uint8Array", "buffer"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/encode-utf8/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = function encodeUtf8 (input) {\n  var result = []\n  var size = input.length\n\n  for (var index = 0; index < size; index++) {\n    var point = input.charCodeAt(index)\n\n    if (point >= 0xD800 && point <= 0xDBFF && size > index + 1) {\n      var second = input.charCodeAt(index + 1)\n\n      if (second >= 0xDC00 && second <= 0xDFFF) {\n        // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        point = (point - 0xD800) * 0x400 + second - 0xDC00 + 0x10000\n        index += 1\n      }\n    }\n\n    // US-ASCII\n    if (point < 0x80) {\n      result.push(point)\n      continue\n    }\n\n    // 2-byte UTF-8\n    if (point < 0x800) {\n      result.push((point >> 6) | 192)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // 3-byte UTF-8\n    if (point < 0xD800 || (point >= 0xE000 && point < 0x10000)) {\n      result.push((point >> 12) | 224)\n      result.push(((point >> 6) & 63) | 128)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // 4-byte UTF-8\n    if (point >= 0x10000 && point <= 0x10FFFF) {\n      result.push((point >> 18) | 240)\n      result.push(((point >> 12) & 63) | 128)\n      result.push(((point >> 6) & 63) | 128)\n      result.push((point & 63) | 128)\n      continue\n    }\n\n    // Invalid character\n    result.push(0xEF, 0xBF, 0xBD)\n  }\n\n  return new Uint8Array(result).buffer\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAU,CAAEC,KAAK,EAAE;EAC3C,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI,GAAGF,KAAK,CAACG,MAAM;EAEvB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,IAAI,EAAEE,KAAK,EAAE,EAAE;IACzC,IAAIC,KAAK,GAAGL,KAAK,CAACM,UAAU,CAACF,KAAK,CAAC;IAEnC,IAAIC,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAIH,IAAI,GAAGE,KAAK,GAAG,CAAC,EAAE;MAC1D,IAAIG,MAAM,GAAGP,KAAK,CAACM,UAAU,CAACF,KAAK,GAAG,CAAC,CAAC;MAExC,IAAIG,MAAM,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,EAAE;QACxC;QACAF,KAAK,GAAG,CAACA,KAAK,GAAG,MAAM,IAAI,KAAK,GAAGE,MAAM,GAAG,MAAM,GAAG,OAAO;QAC5DH,KAAK,IAAI,CAAC;MACZ;IACF;;IAEA;IACA,IAAIC,KAAK,GAAG,IAAI,EAAE;MAChBJ,MAAM,CAACO,IAAI,CAACH,KAAK,CAAC;MAClB;IACF;;IAEA;IACA,IAAIA,KAAK,GAAG,KAAK,EAAE;MACjBJ,MAAM,CAACO,IAAI,CAAEH,KAAK,IAAI,CAAC,GAAI,GAAG,CAAC;MAC/BJ,MAAM,CAACO,IAAI,CAAEH,KAAK,GAAG,EAAE,GAAI,GAAG,CAAC;MAC/B;IACF;;IAEA;IACA,IAAIA,KAAK,GAAG,MAAM,IAAKA,KAAK,IAAI,MAAM,IAAIA,KAAK,GAAG,OAAQ,EAAE;MAC1DJ,MAAM,CAACO,IAAI,CAAEH,KAAK,IAAI,EAAE,GAAI,GAAG,CAAC;MAChCJ,MAAM,CAACO,IAAI,CAAGH,KAAK,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG,CAAC;MACtCJ,MAAM,CAACO,IAAI,CAAEH,KAAK,GAAG,EAAE,GAAI,GAAG,CAAC;MAC/B;IACF;;IAEA;IACA,IAAIA,KAAK,IAAI,OAAO,IAAIA,KAAK,IAAI,QAAQ,EAAE;MACzCJ,MAAM,CAACO,IAAI,CAAEH,KAAK,IAAI,EAAE,GAAI,GAAG,CAAC;MAChCJ,MAAM,CAACO,IAAI,CAAGH,KAAK,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG,CAAC;MACvCJ,MAAM,CAACO,IAAI,CAAGH,KAAK,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG,CAAC;MACtCJ,MAAM,CAACO,IAAI,CAAEH,KAAK,GAAG,EAAE,GAAI,GAAG,CAAC;MAC/B;IACF;;IAEA;IACAJ,MAAM,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC/B;EAEA,OAAO,IAAIC,UAAU,CAACR,MAAM,CAAC,CAACS,MAAM;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}