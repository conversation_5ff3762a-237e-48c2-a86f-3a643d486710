<?php

namespace App\PushNotifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class ScheduleCompleted extends Notification implements ShouldQueue
{
    use Queueable;

    public $tournament;
    public $tournamentName;
    public $totalMatches;
    public $scheduledMatches;
    public $jobId;
    public $executionTime;
    public $initiatedByUser;

    public function via($notifiable)
    {
        return [FcmChannel::class];
    }

    public function __construct($tournament, $totalMatches, $scheduledMatches, $jobId = null, $executionTime = null, $initiatedByUser = null)
    {
        $this->tournament = $tournament;
        $this->tournamentName = $tournament ? $tournament->name : 'Tournament';
        $this->totalMatches = $totalMatches;
        $this->scheduledMatches = $scheduledMatches;
        $this->jobId = $jobId;
        $this->executionTime = $executionTime;
        $this->initiatedByUser = $initiatedByUser;
    }

    public function toFcm($notifiable): FcmMessage
    {
        $title = __('Tournament Schedule Completed');
        $body = __('Tournament ":tournament" has been successfully scheduled. :scheduled of :total matches have been scheduled.', [
            'tournament' => $this->tournamentName,
            'scheduled' => $this->scheduledMatches,
            'total' => $this->totalMatches
        ]);

        // Add execution time if available
        if ($this->executionTime) {
            $body .= ' ' . __('Completed in :time seconds.', ['time' => $this->executionTime]);
        }

        // Add who initiated the action if available
        if ($this->initiatedByUser) {
            $body .= ' ' . __('Initiated by :user.', ['user' => $this->initiatedByUser]);
        }

        $tournament_url = config('constants.tournament_details_url', config('app.client_url') . '/tournaments/' . ($this->tournament ? $this->tournament->id : ''));
        if ($this->tournament) {
            $tournament_url = str_replace('{tournament_id}', $this->tournament->id, $tournament_url);
        }

        return (new FcmMessage(notification: new FcmNotification(
            title: $title,
            body: $body,
            image: config('constants.notification_icon')
        )))
            ->data([
                'go_url' => $tournament_url,
                'tournament_id' => $this->tournament ? $this->tournament->id : null,
                'job_id' => $this->jobId,
                'action_type' => 'schedule_completed'
            ])
            ->custom([
                'android' => [
                    'notification' => [
                        'color' => '#0A0A0A',
                        'sound' => 'default',
                        'priority' => 'high'
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'schedule_completed_android',
                    ],
                    'collapse_key' => 'schedule_completed',
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                            'badge' => 1
                        ]
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'schedule_completed_ios',
                    ],
                ],
                'webpush' => [
                    'fcm_options' => [
                        'analytics_label' => 'schedule_completed_web',
                        'link' => $tournament_url,
                    ],
                    'notification' => [
                        'icon' => config('constants.notification_icon', '/assets/icons/icon-192x192.png'),
                        'badge' => config('constants.notification_badge', '/assets/icons/badge-72x72.png')
                    ]
                ],
            ]);
    }
}
