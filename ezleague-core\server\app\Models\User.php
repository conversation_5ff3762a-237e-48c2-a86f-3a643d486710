<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Http\Controllers\SendMessageController;
use App\Mail\ApproveRegistration;
use App\Mail\CustomMail;
use App\Mail\PlayerValidate;
use App\Mail\Reset2FA;
use App\Mail\ResetPassword;
use App\Mail\SendPassword;
use App\Mail\VerifyEmail;
use App\PushNotifications\AcountActivated;
use App\PushNotifications\CancelMatch;
use App\PushNotifications\CustomMessage;
use App\PushNotifications\ScheduleCompleted;
use App\PushNotifications\UpdateScore;
use Illuminate\Contracts\Notifications\Dispatcher;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Passport\HasApiTokens;
use <PERSON><PERSON>\Cashier\Billable;
use App\Mail\RegistrationCancelled;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, Billable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        // 'project_id',
        'first_name',
        'last_name',
        'email',
        'password',
        'role_id',
        'phone',
        'two_factor_auth',
        'auth_code',
        'firebase_token',
        'last_login',
        'language',
        'accept_rule',
        'consent',
        'verify_email',
    ];

    protected $dates = ['last_login'];

    public function routeNotificationForFcm()
    {
        if ($this->firebase_token && $this->firebase_token != 'null') {
            return $this->firebase_token;
        }
    }

    public function sendNotiCustomMessage($data, SendMessage $send_message)
    {
        try {
            if ($this->firebase_token && $this->firebase_token != 'null') {
                $this->notify((new CustomMessage($data, $send_message))->locale($this->language));
            }
        } catch (\Exception $e) {
            Log::error('sendNotiCustomMessage', ['error' => $e->getMessage()]);
        }
    }


    /**
     * CustomMail
     * @param array $data $data = [
     *      'title' => '',
     *      'greeting' => '',
     *      'content' => '',
     *      'attachments' => [],
     *      'cc' => [],
     *      'bcc' => [],
     *      'subject' => '',
     *      'from' => '',
     *      'replyTo' => '',
     *      'replyToName' => '',
     *      ];
     */
    public function sendCustomMail($data, SendMessage $send_message)
    {
        try {
            $this->notify(new CustomMail($data, $send_message));
        } catch (\Exception $e) {
            Log::error('sendCustomMail', ['error' => $e->getMessage()]);
        }
    }


    public function sendNotiCancelMatch($match)
    {
        try {
            if ($this->firebase_token && $this->firebase_token != 'null') {
                $this->notify((new CancelMatch($match))->locale($this->language));
            }
        } catch (\Exception $e) {
            Log::error('sendNotiCancelMatch', ['error' => $e->getMessage()]);
        }
    }

    public function sendNotiUpdateScore($match, $is_penalty)
    {
        try {
            if ($this->firebase_token && $this->firebase_token != 'null') {
                $this->notify((new UpdateScore($match, $is_penalty))->locale($this->language));
            }
        } catch (\Exception $e) {
            Log::error('sendNotiUpdateScore', ['error' => $e->getMessage()]);
        }
    }

    public function sendNotiScheduleCompleted($tournament, $totalMatches, $scheduledMatches, $jobId = null, $executionTime = null, $initiatedByUser = null)
    {
        try {
            if ($this->firebase_token && $this->firebase_token != 'null') {
                $this->notify((new ScheduleCompleted($tournament, $totalMatches, $scheduledMatches, $jobId, $executionTime, $initiatedByUser))->locale($this->language));
            }
        } catch (\Exception $e) {
            Log::error('sendNotiScheduleCompleted', [
                'user_id' => $this->id,
                'tournament_id' => $tournament ? $tournament->id : null,
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function sendEmailVerificationNotification()
    {
        try {
            $this->notify((new VerifyEmail(true))->locale($this->language));
        } catch (\Exception $e) {
            Log::error('sendEmailVerificationNotification', ['error' => $e->getMessage()]);
        }
    }

    public function sendPasswordResetNotification($token)
    {
        try {
            $this->notify((new ResetPassword($token))->locale($this->language));
        } catch (\Exception $e) {
            Log::error('sendPasswordResetNotification', ['error' => $e->getMessage()]);
        }
    }

    public function sendPasswordNotification($password, $reset = false, User $user)
    {
        try {
            $notification = new SendPassword($password, $reset);

            $notification->updateMailContent($user);

            $notification->locale($this->language);

            $this->notify($notification);

        } catch (\Exception $e) {
            Log::error('sendPasswordNotification', ['error' => $e->getMessage()]);
        }
    }


    public function sendValidateNotificationToGuardian($player, $guardian, $season, $fields_not_accepted)
    {

        Log::info("fields_not_accepted", $fields_not_accepted);
        try {
            // Get custom labels from settings
            $settings = Settings::where('key', 'init_json')->first();
            $custom_fields = $settings->value['custom_fields'] ?? [];
            $custom_labels = [];

            foreach ($custom_fields as $custom_field) {
                $custom_labels[$custom_field['key']] = $custom_field['props']['label'] ?? $custom_field['key'];
            }

            // Build HTML for rejected fields
            $fields_str = '';
            foreach ($fields_not_accepted as $field) {
                $field_key = $field['field'];
                $label = $custom_labels[$field_key] ?? __('validation.attributes.' . $field_key);
                $label = mb_strtoupper(mb_substr($label, 0, 1)) . mb_substr($label, 1);
                $message = e(__($field['message']));
                $fields_str .= '<p style="margin-bottom: 0.5rem"><b>' . $label . ':</b> ' . $message . '</p>';
            }

            Log::info("fields_str", ['fields_str' => $fields_str]);
            // Fetch the email template
            $template = EmailTemplate::where('name', 'Player Registration Incomplete')->first();
            if (!$template) {
                throw new \Exception("Email template 'Player Registration Incomplete' not found or inactive.");
            }

            // Variables for replacement
            $variables = [
                '{{player_name}}' => $player->first_name . ' ' . $player->last_name,
                '{{guardian_name}}' => $guardian->first_name . ' ' . $guardian->last_name,
                '{{season_name}}' => $season->name,
                '{{app_name}}' => config('app.name'),
                '{{fields_not_accepted}}' => $fields_str
            ];

            Log::info("variables", ['variables' => $variables]);

            // Apply replacements
            $subject = strtr($template->subject, $variables);
            $content = strtr($template->content, $variables);
            $content = preg_replace('/<span class="mention" data-mention="[^"]*">([^<]*)<\/span>/', '$1', $content);




            Log::info("subject", ['subject' => $subject]);
            Log::info("content", ['content' => $content]);

            // Build the message
            $message = [
                'title' => $subject,
                'content' => $content,
                'type' => 'email_push_noti',
                'user_ids' => "$player->id,$guardian->id",
            ];

            Log::info("message", ['message' => $message]);

            // Send the message
            $request = new \Illuminate\Http\Request();
            $request->replace($message);
            $SendMessageController = new SendMessageController();
            return $SendMessageController->sendCustomMessage($request);

        } catch (\Exception $e) {
            Log::error('sendValidateNotificationToGuardian', ['error' => $e->getMessage()]);
        }
    }



    private function sendNotificationToAdmin($user_id)
    {
        $message = [
            'title' => "New player approve registration",
            'content' => "New player approve registration",
            'greeting' => "",
            'type' => 'push_noti',
            'user_ids' => "$user_id",
        ];

        $request = new \Illuminate\Http\Request();
        $request->replace($message);
        $SendMessageController = new SendMessageController();
        $SendMessageController->sendCustomMessage($request);
    }

    public function sendApproveRegistrationNotification($player, $guardian, $season, $payment = null, $user_id = null)
    {
        try {

            // Gửi notification cho guardian bằng mail, dùng queue
            if ($guardian && $guardian->email) {
                $guardian->notify(new ApproveRegistration($player, $guardian, $season, $payment));
            }

            // Gửi thông báo cho admin nếu cần
            try {
                $this->sendNotificationToAdmin($user_id);
            } catch (\Exception $e) {
                Log::error('sendNotificationToAdmin', ['error' => $e->getMessage()]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('sendApproveRegistrationNotification', ['error' => $e->getMessage()]);
            return false;
        }
    }

    public function sendCancelRegistrationNotification($player, $guardian, $season, $reason = null, $user_id = null)
    {
        try {
            // Gửi notification cho guardian bằng mail, dùng queue
            if ($guardian && $guardian->email) {
                $guardian->notify(new RegistrationCancelled($player, $guardian, $season, $reason));
            }


            return true;
        } catch (\Exception $e) {
            Log::error('sendCancelRegistrationNotification', ['error' => $e->getMessage()]);
            return false;
        }
    }

    public function sendResetCode2FA()
    {
        try {
            $this->notify((new Reset2FA($this))->locale($this->language));
        } catch (\Exception $e) {
            Log::error('sendResetCode2FA', ['error' => $e->getMessage()]);
        }
    }


    // primary key
    protected $primaryKey = 'id';

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'auth_code'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Get the role that owns the user.
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get role permissions for the user.
     */
    public function rolePermissions()
    {
        return $this->role->rolePermissions;
    }

    public function teamsClub()
    {
        return $this->hasManyThrough(
            Team::class,
            UserClub::class,
            'user_id',
            'club_id',
            'id',
            'club_id'
        );
    }

    public function teamsCoach()
    {
        return $this->hasManyThrough(
            Team::class,
            TeamCoach::class,
            'user_id',
            'id',
            'id',
            'team_id'
        );
    }

    // clubs
    public function clubs()
    {
        return $this->belongsToMany(Club::class, 'user_clubs');
    }

    /**
     * Get the player that owns the user.
     */
    public function player()
    {
        return $this->hasOne(Player::class);
    }

    public function guardian()
    {
        return $this->hasMany(Guardian::class);
    }

    public function guardianPlayers()
    {
        return $this->hasManyThrough(
            Player::class,
            Guardian::class,
            'user_id',
            'id',
            'id',
            'player_id'
        );
    }

    public function userClubs()
    {
        return $this->hasMany(UserClub::class);
    }

    // link to stage_matches table by field scores_updated_by
    public function stageMatches()
    {
        return $this->hasMany(StageMatch::class, 'scores_updated_by');
    }

    // favourite teams through favourite_teams
    public function favouriteTeams()
    {
        return $this->hasManyThrough(
            Team::class,
            FavouriteTeam::class,
            'user_id',
            'id',
            'id',
            'team_id'
        );
    }

    // favourite players through favouurite_clubs
    public function favouriteClubs()
    {
        return $this->hasManyThrough(
            Club::class,
            FavouriteClub::class,
            'user_id',
            'id',
            'id',
            'club_id'
        );
    }


    // is admin
    public function isAdmin()
    {
        return $this->role_id == config('constants.role_base.admin');
    }

    // is parent
    public function isParent()
    {
        return $this->role_id == config('constants.role_base.parent');
    }

    // Route notifications for the mail channel to the email address
    public function routeNotificationForMail(Notification $notification)
    {
        if (config('app.testing')) {
            return config('mail.email_test.address');
        }
        if (isset($notification->isVerification) && $notification->isVerification) {
            return $this->verify_email;
        }

        return $this->email;
    }

    /**
     * Send the given notification immediately.
     *
     * @param mixed $instance
     * @param array|null $channels
     * @return void
     */
    public function notifyNow($instance, array $channels = null)
    {
        // if env TESTING is true, send to mail test
        if (config('app.testing')) {
            $this->email = (config('mail.email_test.address'));
        }
        app(Dispatcher::class)->sendNow($this, $instance, $channels);
    }
}
