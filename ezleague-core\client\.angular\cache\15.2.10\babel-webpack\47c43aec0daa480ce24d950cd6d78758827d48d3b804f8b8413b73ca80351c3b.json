{"ast": null, "code": "import identity from './identity.js';\nimport metaMap from './_metaMap.js';\n\n/**\n * The base implementation of `setData` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to associate metadata with.\n * @param {*} data The metadata.\n * @returns {Function} Returns `func`.\n */\nvar baseSetData = !metaMap ? identity : function (func, data) {\n  metaMap.set(func, data);\n  return func;\n};\nexport default baseSetData;", "map": {"version": 3, "names": ["identity", "metaMap", "baseSetData", "func", "data", "set"], "sources": ["D:/Code/Work/ezactive-vn/ezleague-core/client/node_modules/lodash-es/_baseSetData.js"], "sourcesContent": ["import identity from './identity.js';\nimport metaMap from './_metaMap.js';\n\n/**\n * The base implementation of `setData` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to associate metadata with.\n * @param {*} data The metadata.\n * @returns {Function} Returns `func`.\n */\nvar baseSetData = !metaMap ? identity : function(func, data) {\n  metaMap.set(func, data);\n  return func;\n};\n\nexport default baseSetData;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,CAACD,OAAO,GAAGD,QAAQ,GAAG,UAASG,IAAI,EAAEC,IAAI,EAAE;EAC3DH,OAAO,CAACI,GAAG,CAACF,IAAI,EAAEC,IAAI,CAAC;EACvB,OAAOD,IAAI;AACb,CAAC;AAED,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}